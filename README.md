# SmartInput - 智能语音输入系统

一个跨平台的语音输入解决方案，让您可以通过手机进行语音输入，将转换的文字发送到任何设备的输入框中。

## 功能特性

- 🎤 **语音识别**: 支持实时语音转文字
- 📱 **手机端应用**: PWA应用，支持离线使用
- 🌐 **浏览器插件**: 自动识别网页输入框并注入文字
- 💻 **桌面客户端**: 系统级文字输入支持
- 🔗 **设备配对**: 二维码扫描快速配对
- 🔒 **安全传输**: WebSocket加密通信

## 系统架构

```
┌─────────────────┐    WebSocket    ┌─────────────────┐
│   手机端应用     │ ◄──────────────► │   WebSocket     │
│   (PWA)        │                 │   服务器        │
└─────────────────┘                 └─────────────────┘
                                           │
                                           │ WebSocket
                                           ▼
                    ┌─────────────────┬─────────────────┐
                    │   浏览器插件     │   桌面客户端     │
                    │   (Extension)   │   (Electron)    │
                    └─────────────────┴─────────────────┘
```

## 项目结构

```
smartinput/
├── server/                 # WebSocket服务器
│   ├── src/
│   ├── package.json
│   └── README.md
├── mobile-app/            # 手机端PWA应用
│   ├── src/
│   ├── public/
│   ├── package.json
│   └── README.md
├── browser-extension/     # 浏览器扩展
│   ├── src/
│   ├── manifest.json
│   └── README.md
├── desktop-client/        # 桌面客户端
│   ├── src/
│   ├── package.json
│   └── README.md
└── docs/                  # 文档
    ├── api.md
    ├── setup.md
    └── architecture.md
```

## 快速开始

### 1. 启动服务器
```bash
cd server
npm install
npm start
```

### 2. 启动移动端应用
```bash
cd mobile-app
npm install
npm run dev
```

### 3. 安装浏览器扩展
1. 打开Chrome扩展管理页面
2. 启用开发者模式
3. 加载已解压的扩展程序，选择 `browser-extension` 目录

### 4. 安装桌面客户端
```bash
cd desktop-client
npm install
npm run build
npm run start
```

## 使用流程

1. **启动服务器**: 运行WebSocket服务器
2. **打开手机应用**: 在手机浏览器中访问PWA应用
3. **扫描配对**: 使用手机扫描桌面/浏览器显示的二维码
4. **语音输入**: 在手机上按住录音按钮进行语音输入
5. **自动输入**: 转换的文字自动输入到目标设备的输入框

## 技术栈

- **后端**: Node.js, Express, Socket.io, 语音识别API
- **前端**: React, PWA, Web Speech API
- **浏览器扩展**: Chrome Extension API, WebExtension
- **桌面应用**: Electron, Node.js
- **通信**: WebSocket, HTTPS

## 🔧 开发状态

### ✅ 已完成
- [x] 项目架构设计
- [x] WebSocket服务器完整开发
- [x] 移动端PWA应用完整开发
- [x] 设备注册和管理系统
- [x] 语音识别服务集成
- [x] 实时通信系统
- [x] QR码配对机制
- [x] 响应式UI设计
- [x] 状态管理(Zustand)
- [x] 多语言语音识别支持
- [x] 设备能力检测
- [x] 语音输入历史记录
- [x] Toast通知系统
- [x] 设置页面和统计功能

### 🚧 开发中
- [ ] 浏览器扩展开发
- [ ] 桌面客户端开发
- [ ] 高级语音处理优化

### 📋 待开发
- [ ] 用户账户系统
- [ ] 设备权限管理
- [ ] 语音命令支持
- [ ] 多设备同步
- [ ] 性能优化和缓存
- [ ] 安全性增强

## 🧪 测试

运行连接测试：

```bash
node test-connection.js
```

当前系统已经可以正常运行：
- ✅ 服务器运行在 `http://localhost:3001`
- ✅ 移动端应用运行在 `http://localhost:3000`
- ✅ 设备连接和注册功能正常
- ✅ 语音识别功能完整

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

MIT License
