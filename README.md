# SmartInput - 智能语音输入系统

一个跨平台的语音输入解决方案，让您可以通过手机进行语音输入，将转换的文字发送到任何设备的输入框中。

## 功能特性

- 🎤 **语音识别**: 支持实时语音转文字
- 📱 **手机端应用**: PWA应用，支持离线使用
- 🌐 **浏览器插件**: 自动识别网页输入框并注入文字
- 💻 **桌面客户端**: 系统级文字输入支持
- 🔗 **设备配对**: 二维码扫描快速配对
- 🔒 **安全传输**: WebSocket加密通信

## 系统架构

```
┌─────────────────┐    WebSocket    ┌─────────────────┐
│   手机端应用     │ ◄──────────────► │   WebSocket     │
│   (PWA)        │                 │   服务器        │
└─────────────────┘                 └─────────────────┘
                                           │
                                           │ WebSocket
                                           ▼
                    ┌─────────────────┬─────────────────┐
                    │   浏览器插件     │   桌面客户端     │
                    │   (Extension)   │   (Electron)    │
                    └─────────────────┴─────────────────┘
```

## 项目结构

```
smartinput/
├── server/                 # WebSocket服务器
│   ├── src/
│   ├── package.json
│   └── README.md
├── mobile-app/            # 手机端PWA应用
│   ├── src/
│   ├── public/
│   ├── package.json
│   └── README.md
├── browser-extension/     # 浏览器扩展
│   ├── src/
│   ├── manifest.json
│   └── README.md
├── desktop-client/        # 桌面客户端
│   ├── src/
│   ├── package.json
│   └── README.md
└── docs/                  # 文档
    ├── api.md
    ├── setup.md
    └── architecture.md
```

## 快速开始

### 1. 启动服务器
```bash
cd server
npm install
npm start
```

### 2. 启动移动端应用
```bash
cd mobile-app
npm install
npm run dev
```

### 3. 安装浏览器扩展

#### 准备图标文件
1. 在浏览器中打开 `browser-extension/generate-icons.html`
2. 点击"下载所有图标"按钮
3. 将下载的PNG文件保存到 `browser-extension/icons/` 目录

#### 安装扩展
1. 打开Chrome扩展管理页面 (`chrome://extensions/`)
2. 启用"开发者模式"开关
3. 点击"加载已解压的扩展程序"
4. 选择 `browser-extension` 目录

### 4. 安装桌面客户端
```bash
cd desktop-client
npm install
npm run build
npm run start
```

## 📱 使用流程

1. **启动服务器**: 运行WebSocket服务器 (`npm start` 在server目录)
2. **打开移动应用**: 在手机浏览器中访问 `http://localhost:3000`
3. **自动连接**: 应用会自动注册设备并连接到服务器
4. **开始配对**: 点击"开始配对"按钮生成QR码或手动输入配对码
5. **语音输入**: 点击麦克风按钮开始语音识别
6. **文字传输**: 识别的文字会自动发送到配对的设备

### 主要功能
- **实时语音识别**: 支持中文、英文等多种语言
- **设备配对**: QR码扫描或手动输入配对码
- **智能文字填充**: 自动检测网页输入框并填充文字
- **浏览器扩展**: 支持所有类型的网页输入框
- **历史记录**: 查看和管理语音输入历史
- **设置配置**: 调整语音识别参数和应用设置

### 浏览器扩展使用
1. **点击扩展图标**: 在浏览器工具栏点击SmartInput图标
2. **开始配对**: 点击"开始配对"生成QR码
3. **扫描配对**: 用手机应用扫描QR码完成配对
4. **点击输入框**: 在网页中点击任意文本输入框
5. **语音输入**: 在手机上进行语音输入，文字自动填充到网页

## 🛠️ 技术栈

### 后端服务器
- **Node.js** + **Express** - 服务器框架
- **Socket.io** - WebSocket实时通信
- **UUID** - 设备唯一标识
- **QRCode** - 配对二维码生成
- **CORS** - 跨域资源共享
- **Helmet** - 安全中间件

### 移动端应用
- **React** + **Vite** - 前端框架和构建工具
- **Zustand** - 轻量级状态管理
- **Tailwind CSS** - 原子化CSS框架
- **Lucide React** - 现代图标库
- **QR Scanner** - 二维码扫描功能
- **Web Speech API** - 浏览器原生语音识别
- **PWA** - 渐进式Web应用

### 开发工具
- **ESLint** - 代码质量检查
- **Prettier** - 代码格式化
- **Vite** - 快速构建工具
- **npm** - 包管理器

## 🔧 开发状态

### ✅ 已完成
- [x] 项目架构设计
- [x] WebSocket服务器完整开发
- [x] 移动端PWA应用完整开发
- [x] 设备注册和管理系统
- [x] 语音识别服务集成
- [x] 实时通信系统
- [x] QR码配对机制
- [x] 响应式UI设计
- [x] 状态管理(Zustand)
- [x] 多语言语音识别支持
- [x] 设备能力检测
- [x] 语音输入历史记录
- [x] Toast通知系统
- [x] 设置页面和统计功能

### 🚧 开发中
- [x] 浏览器扩展基础开发（已完成核心功能）
- [ ] 桌面客户端开发
- [ ] 高级语音处理优化

### 📋 待开发
- [ ] 用户账户系统
- [ ] 设备权限管理
- [ ] 语音命令支持
- [ ] 多设备同步
- [ ] 性能优化和缓存
- [ ] 安全性增强

## 🧪 测试

运行连接测试：

```bash
node test-connection.js
```

当前系统已经可以正常运行：
- ✅ 服务器运行在 `http://localhost:3001`
- ✅ 移动端应用运行在 `http://localhost:3000`
- ✅ 设备连接和注册功能正常
- ✅ 语音识别功能完整

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

MIT License
