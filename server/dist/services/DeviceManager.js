"use strict";
const types_1 = require("../types");
const config_1 = require("../config");
class DeviceManager {
    constructor() {
        this.devices = new Map();
        this.socketToDevice = new Map();
        this.cleanupInterval = setInterval(() => {
            this.cleanupOfflineDevices();
        }, config_1.config.cleanup.deviceTimeout);
    }
    registerDevice(socketId, deviceInfo) {
        try {
            const deviceId = this.generateDeviceId(deviceInfo.type, deviceInfo.name);
            const timestamp = Date.now();
            const registeredDevice = {
                ...deviceInfo,
                id: deviceId,
                timestamp,
                socketId,
                lastSeen: timestamp,
                isOnline: true
            };
            this.validateDeviceInfo(registeredDevice);
            if (this.devices.has(deviceId)) {
                const existingDevice = this.devices.get(deviceId);
                registeredDevice.timestamp = existingDevice.timestamp;
            }
            this.devices.set(deviceId, registeredDevice);
            this.socketToDevice.set(socketId, deviceId);
            this.log('info', `设备注册成功: ${deviceInfo.type} - ${deviceInfo.name} (${deviceId})`, {
                deviceId,
                socketId,
                deviceType: deviceInfo.type
            });
            return registeredDevice;
        }
        catch (error) {
            this.log('error', `设备注册失败: ${error instanceof Error ? error.message : 'Unknown error'}`, {
                socketId,
                deviceInfo
            });
            throw error;
        }
    }
    unregisterDevice(socketId) {
        const deviceId = this.socketToDevice.get(socketId);
        if (!deviceId) {
            return false;
        }
        const device = this.devices.get(deviceId);
        if (device) {
            device.isOnline = false;
            device.lastSeen = Date.now();
            this.log('info', `设备断开连接: ${device.type} - ${device.name} (${deviceId})`, {
                deviceId,
                socketId,
                deviceType: device.type
            });
        }
        this.socketToDevice.delete(socketId);
        return true;
    }
    getDevice(deviceId) {
        return this.devices.get(deviceId);
    }
    getDeviceBySocketId(socketId) {
        const deviceId = this.socketToDevice.get(socketId);
        return deviceId ? this.devices.get(deviceId) : undefined;
    }
    getOnlineDevices() {
        return Array.from(this.devices.values()).filter(device => device.isOnline);
    }
    getAllDevices() {
        return Array.from(this.devices.values());
    }
    getDevicesByType(type) {
        return Array.from(this.devices.values()).filter(device => device.type === type);
    }
    getDevicesByCapability(capability) {
        return Array.from(this.devices.values()).filter(device => device.capabilities[capability]);
    }
    updateDeviceActivity(deviceId) {
        const device = this.devices.get(deviceId);
        if (device) {
            device.lastSeen = Date.now();
        }
    }
    isDeviceOnline(deviceId) {
        const device = this.devices.get(deviceId);
        return device ? device.isOnline : false;
    }
    getStats() {
        const allDevices = this.getAllDevices();
        const onlineDevices = this.getOnlineDevices();
        return {
            totalDevices: allDevices.length,
            onlineDevices: onlineDevices.length,
            devicesByType: {
                mobile: allDevices.filter(d => d.type === 'mobile').length,
                browser: allDevices.filter(d => d.type === 'browser').length,
                desktop: allDevices.filter(d => d.type === 'desktop').length
            },
            onlineByType: {
                mobile: onlineDevices.filter(d => d.type === 'mobile').length,
                browser: onlineDevices.filter(d => d.type === 'browser').length,
                desktop: onlineDevices.filter(d => d.type === 'desktop').length
            }
        };
    }
    getDeviceCount() {
        return this.getOnlineDevices().length;
    }
    getDevicesStatus() {
        const devices = this.getAllDevices();
        const onlineDevices = this.getOnlineDevices();
        return {
            total: devices.length,
            online: onlineDevices.length,
            byType: {
                mobile: devices.filter(d => d.type === 'mobile').length,
                browser: devices.filter(d => d.type === 'browser').length,
                desktop: devices.filter(d => d.type === 'desktop').length
            },
            onlineByType: {
                mobile: onlineDevices.filter(d => d.type === 'mobile').length,
                browser: onlineDevices.filter(d => d.type === 'browser').length,
                desktop: onlineDevices.filter(d => d.type === 'desktop').length
            }
        };
    }
    cleanupOfflineDevices() {
        const now = Date.now();
        const timeout = config_1.config.cleanup.deviceTimeout;
        let cleanedCount = 0;
        for (const [deviceId, device] of this.devices.entries()) {
            if (!device.isOnline && (now - device.lastSeen) > timeout) {
                this.devices.delete(deviceId);
                cleanedCount++;
                this.log('debug', `清理离线设备: ${device.type} - ${device.name} (${deviceId})`, {
                    deviceId,
                    lastSeen: device.lastSeen,
                    offlineTime: now - device.lastSeen
                });
            }
        }
        if (cleanedCount > 0) {
            this.log('info', `清理了 ${cleanedCount} 个离线设备`);
        }
    }
    generateDeviceId(type, name) {
        const timestamp = Date.now().toString(36);
        const random = Math.random().toString(36).substring(2, 8);
        return `${type}_${name.toLowerCase().replace(/[^a-z0-9]/g, '')}_${timestamp}${random}`;
    }
    validateDeviceInfo(device) {
        if (!device.id || typeof device.id !== 'string') {
            throw new types_1.SmartInputError('Invalid device ID', 'INVALID_DEVICE_ID', 400);
        }
        if (!device.type || !['mobile', 'browser', 'desktop'].includes(device.type)) {
            throw new types_1.SmartInputError('Invalid device type', 'INVALID_DEVICE_TYPE', 400);
        }
        if (!device.name || typeof device.name !== 'string' || device.name.trim().length === 0) {
            throw new types_1.SmartInputError('Invalid device name', 'INVALID_DEVICE_NAME', 400);
        }
        if (!device.capabilities || typeof device.capabilities !== 'object') {
            throw new types_1.SmartInputError('Invalid device capabilities', 'INVALID_CAPABILITIES', 400);
        }
        const { capabilities } = device;
        if (device.type === 'mobile' && !capabilities.canSendVoice) {
            throw new types_1.SmartInputError('Mobile devices must support voice sending', 'INVALID_MOBILE_CAPABILITIES', 400);
        }
        if (device.type === 'browser' && !capabilities.canReceiveText) {
            throw new types_1.SmartInputError('Browser devices must support text receiving', 'INVALID_BROWSER_CAPABILITIES', 400);
        }
    }
    log(level, message, metadata) {
        const logEntry = {
            level,
            message,
            timestamp: Date.now(),
            metadata: metadata || undefined
        };
        console.log(`[${level.toUpperCase()}] ${message}`, metadata ? JSON.stringify(metadata) : '');
    }
    destroy() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
        }
        this.devices.clear();
        this.socketToDevice.clear();
    }
}
module.exports = DeviceManager;
