import { PairingCode, PairingData } from '../types';
import DeviceManager = require('./DeviceManager');
declare class PairingManager {
    private deviceManager;
    private pairingCodes;
    private pairingAttempts;
    private cleanupInterval;
    constructor(deviceManager: DeviceManager);
    generatePairingCode(deviceId: string): Promise<{
        code: string;
        qrCodeUrl: string;
        expiresAt: number;
    }>;
    usePairingCode(code: string, requestingDeviceId: string): Promise<PairingData>;
    validatePairingCode(code: string): boolean;
    getPairingCodeInfo(code: string): PairingCode | undefined;
    clearDevicePairingCodes(deviceId: string): void;
    getStats(): {
        totalCodes: number;
        activeCodes: number;
        usedCodes: number;
        expiredCodes: number;
    };
    private generateCode;
    private cleanupExpiredCodes;
    private cleanupPairingAttempts;
    private log;
    destroy(): void;
}
export = PairingManager;
