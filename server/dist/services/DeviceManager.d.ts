import { DeviceInfo, RegisteredDevice, DeviceType, DeviceCapabilities } from '../types';
declare class DeviceManager {
    private devices;
    private socketToDevice;
    private cleanupInterval;
    constructor();
    registerDevice(socketId: string, deviceInfo: Omit<DeviceInfo, 'id' | 'timestamp'>): RegisteredDevice;
    unregisterDevice(socketId: string): boolean;
    getDevice(deviceId: string): RegisteredDevice | undefined;
    getDeviceBySocketId(socketId: string): RegisteredDevice | undefined;
    getOnlineDevices(): RegisteredDevice[];
    getAllDevices(): RegisteredDevice[];
    getDevicesByType(type: DeviceType): RegisteredDevice[];
    getDevicesByCapability(capability: keyof DeviceCapabilities): RegisteredDevice[];
    updateDeviceActivity(deviceId: string): void;
    isDeviceOnline(deviceId: string): boolean;
    getStats(): {
        totalDevices: number;
        onlineDevices: number;
        devicesByType: {
            mobile: number;
            browser: number;
            desktop: number;
        };
        onlineByType: {
            mobile: number;
            browser: number;
            desktop: number;
        };
    };
    getDeviceCount(): number;
    getDevicesStatus(): {
        total: number;
        online: number;
        byType: {
            mobile: number;
            browser: number;
            desktop: number;
        };
        onlineByType: {
            mobile: number;
            browser: number;
            desktop: number;
        };
    };
    private cleanupOfflineDevices;
    private generateDeviceId;
    private validateDeviceInfo;
    private log;
    destroy(): void;
}
export = DeviceManager;
