"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const qrcode_1 = __importDefault(require("qrcode"));
const types_1 = require("../types");
const config_1 = require("../config");
class PairingManager {
    constructor(deviceManager) {
        this.deviceManager = deviceManager;
        this.pairingCodes = new Map();
        this.pairingAttempts = new Map();
        this.cleanupInterval = setInterval(() => {
            this.cleanupExpiredCodes();
        }, config_1.config.cleanup.pairingCodeCleanup);
    }
    async generatePairingCode(deviceId) {
        try {
            const device = this.deviceManager.getDevice(deviceId);
            if (!device) {
                throw new types_1.SmartInputError('Device not found', 'DEVICE_NOT_FOUND', 404);
            }
            const code = this.generateCode();
            const expiresAt = Date.now() + config_1.config.pairing.expirationTime;
            this.clearDevicePairingCodes(deviceId);
            const pairingCode = {
                code,
                deviceId,
                expiresAt,
                isUsed: false
            };
            this.pairingCodes.set(code, pairingCode);
            const qrData = JSON.stringify({
                type: 'smartinput-pairing',
                code,
                deviceId,
                deviceName: device.name,
                expiresAt
            });
            const qrCodeUrl = await qrcode_1.default.toDataURL(qrData, {
                errorCorrectionLevel: 'M',
                margin: 1,
                color: {
                    dark: '#000000',
                    light: '#FFFFFF'
                },
                width: 256
            });
            this.log('info', `配对码生成成功: ${code} for device ${deviceId}`, {
                deviceId,
                code,
                expiresAt
            });
            return { code, qrCodeUrl, expiresAt };
        }
        catch (error) {
            this.log('error', `配对码生成失败: ${error instanceof Error ? error.message : 'Unknown error'}`, {
                deviceId
            });
            throw error;
        }
    }
    async usePairingCode(code, requestingDeviceId) {
        try {
            const pairingCode = this.pairingCodes.get(code);
            if (!pairingCode) {
                throw new types_1.SmartInputError('Invalid pairing code', 'INVALID_PAIRING_CODE', 400);
            }
            if (Date.now() > pairingCode.expiresAt) {
                this.pairingCodes.delete(code);
                throw new types_1.SmartInputError('Pairing code expired', 'PAIRING_CODE_EXPIRED', 400);
            }
            if (pairingCode.isUsed) {
                throw new types_1.SmartInputError('Pairing code already used', 'PAIRING_CODE_USED', 400);
            }
            const requestingDevice = this.deviceManager.getDevice(requestingDeviceId);
            if (!requestingDevice) {
                throw new types_1.SmartInputError('Requesting device not found', 'REQUESTING_DEVICE_NOT_FOUND', 404);
            }
            const targetDevice = this.deviceManager.getDevice(pairingCode.deviceId);
            if (!targetDevice) {
                this.pairingCodes.delete(code);
                throw new types_1.SmartInputError('Target device not found', 'TARGET_DEVICE_NOT_FOUND', 404);
            }
            if (!this.deviceManager.isDeviceOnline(pairingCode.deviceId)) {
                throw new types_1.SmartInputError('Target device is offline', 'TARGET_DEVICE_OFFLINE', 400);
            }
            const attemptKey = `${requestingDeviceId}:${code}`;
            const attempts = this.pairingAttempts.get(attemptKey) || 0;
            if (attempts >= config_1.config.pairing.maxAttempts) {
                throw new types_1.SmartInputError('Too many pairing attempts', 'TOO_MANY_ATTEMPTS', 429);
            }
            this.pairingAttempts.set(attemptKey, attempts + 1);
            pairingCode.isUsed = true;
            this.cleanupPairingAttempts(requestingDeviceId);
            const pairingData = {
                success: true,
                deviceId: targetDevice.id,
                deviceName: targetDevice.name
            };
            this.log('info', `配对成功: ${requestingDevice.name} (${requestingDeviceId}) <-> ${targetDevice.name} (${targetDevice.id})`, {
                requestingDeviceId,
                targetDeviceId: targetDevice.id,
                code
            });
            return pairingData;
        }
        catch (error) {
            this.log('error', `配对失败: ${error instanceof Error ? error.message : 'Unknown error'}`, {
                code,
                requestingDeviceId
            });
            throw error;
        }
    }
    validatePairingCode(code) {
        const pairingCode = this.pairingCodes.get(code);
        if (!pairingCode) {
            return false;
        }
        if (Date.now() > pairingCode.expiresAt || pairingCode.isUsed) {
            this.pairingCodes.delete(code);
            return false;
        }
        return true;
    }
    getPairingCodeInfo(code) {
        const pairingCode = this.pairingCodes.get(code);
        if (!pairingCode) {
            return undefined;
        }
        if (Date.now() > pairingCode.expiresAt) {
            this.pairingCodes.delete(code);
            return undefined;
        }
        return pairingCode;
    }
    clearDevicePairingCodes(deviceId) {
        const codesToDelete = [];
        for (const [code, pairingCode] of this.pairingCodes.entries()) {
            if (pairingCode.deviceId === deviceId) {
                codesToDelete.push(code);
            }
        }
        for (const code of codesToDelete) {
            this.pairingCodes.delete(code);
        }
        if (codesToDelete.length > 0) {
            this.log('debug', `清理设备配对码: ${deviceId}, 清理数量: ${codesToDelete.length}`);
        }
    }
    getStats() {
        const now = Date.now();
        const activeCodes = Array.from(this.pairingCodes.values()).filter(code => !code.isUsed && code.expiresAt > now);
        return {
            totalCodes: this.pairingCodes.size,
            activeCodes: activeCodes.length,
            usedCodes: Array.from(this.pairingCodes.values()).filter(code => code.isUsed).length,
            expiredCodes: Array.from(this.pairingCodes.values()).filter(code => !code.isUsed && code.expiresAt <= now).length
        };
    }
    generateCode() {
        const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        let result = '';
        for (let i = 0; i < config_1.config.pairing.codeLength; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        if (this.pairingCodes.has(result)) {
            return this.generateCode();
        }
        return result;
    }
    cleanupExpiredCodes() {
        const now = Date.now();
        const codesToDelete = [];
        for (const [code, pairingCode] of this.pairingCodes.entries()) {
            if (now > pairingCode.expiresAt) {
                codesToDelete.push(code);
            }
        }
        for (const code of codesToDelete) {
            this.pairingCodes.delete(code);
        }
        if (codesToDelete.length > 0) {
            this.log('debug', `清理过期配对码: ${codesToDelete.length} 个`);
        }
    }
    cleanupPairingAttempts(deviceId) {
        const keysToDelete = [];
        for (const key of this.pairingAttempts.keys()) {
            if (key.startsWith(`${deviceId}:`)) {
                keysToDelete.push(key);
            }
        }
        for (const key of keysToDelete) {
            this.pairingAttempts.delete(key);
        }
    }
    log(level, message, metadata) {
        const logEntry = {
            level,
            message,
            timestamp: Date.now(),
            metadata: metadata || undefined
        };
        console.log(`[${level.toUpperCase()}] ${message}`, metadata ? JSON.stringify(metadata) : '');
    }
    destroy() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
        }
        this.pairingCodes.clear();
        this.pairingAttempts.clear();
    }
}
module.exports = PairingManager;
