"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const http_1 = require("http");
const socket_io_1 = require("socket.io");
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const config_1 = require("./config");
const types_1 = require("./types");
const DeviceManager = require("./services/DeviceManager");
const PairingManager = require("./services/PairingManager");
class SmartInputServer {
    constructor() {
        (0, config_1.validateConfig)();
        this.app = (0, express_1.default)();
        this.server = (0, http_1.createServer)(this.app);
        this.io = new socket_io_1.Server(this.server, { cors: config_1.config.cors });
        this.deviceManager = new DeviceManager();
        this.pairingManager = new PairingManager(this.deviceManager);
        this.startTime = Date.now();
        this.stats = this.initializeStats();
        this.setupMiddleware();
        this.setupRoutes();
        this.setupSocketHandlers();
        this.setupErrorHandlers();
    }
    initializeStats() {
        return {
            uptime: 0,
            connectedDevices: 0,
            totalConnections: 0,
            messagesProcessed: 0,
            pairingAttempts: 0,
            successfulPairings: 0,
            errors: 0,
            lastActivity: Date.now()
        };
    }
    setupMiddleware() {
        if (!config_1.isDevelopment) {
            this.app.use((0, helmet_1.default)());
        }
        this.app.use((0, cors_1.default)(config_1.config.cors));
        this.app.use(express_1.default.json({ limit: '10mb' }));
        this.app.use(express_1.default.urlencoded({ extended: true }));
        this.app.use((req, res, next) => {
            console.log(`${new Date().toISOString()} ${req.method} ${req.path}`);
            next();
        });
    }
    setupRoutes() {
        this.app.get('/health', (req, res) => {
            const response = {
                success: true,
                data: {
                    status: 'ok',
                    timestamp: new Date().toISOString(),
                    uptime: Date.now() - this.startTime,
                    connectedDevices: this.deviceManager.getDeviceCount(),
                    version: '1.0.0'
                },
                timestamp: Date.now()
            };
            res.json(response);
        });
        this.app.get('/api/stats', (req, res) => {
            const deviceStats = this.deviceManager.getDevicesStatus();
            const pairingStats = this.pairingManager.getStats();
            const response = {
                success: true,
                data: {
                    server: {
                        ...this.stats,
                        uptime: Date.now() - this.startTime
                    },
                    devices: deviceStats,
                    pairing: pairingStats
                },
                timestamp: Date.now()
            };
            res.json(response);
        });
        this.app.get('/api/devices', (req, res) => {
            const devices = this.deviceManager.getOnlineDevices().map(device => ({
                id: device.id,
                type: device.type,
                name: device.name,
                capabilities: device.capabilities,
                isOnline: device.isOnline,
                lastSeen: device.lastSeen
            }));
            const response = {
                success: true,
                data: devices,
                timestamp: Date.now()
            };
            res.json(response);
        });
        this.app.use('*', (req, res) => {
            const response = {
                success: false,
                error: {
                    code: 'NOT_FOUND',
                    message: `Route ${req.originalUrl} not found`,
                    timestamp: Date.now()
                }
            };
            res.status(404).json(response);
        });
    }
    setupSocketHandlers() {
        this.io.on('connection', (socket) => {
            console.log(`新设备连接: ${socket.id}`);
            this.stats.totalConnections++;
            this.updateConnectedDevicesCount();
            socket.on('device:register', async (deviceInfo) => {
                try {
                    const device = this.deviceManager.registerDevice(socket.id, deviceInfo);
                    socket.data.deviceId = device.id;
                    socket.data.deviceType = device.type;
                    socket.data.deviceName = device.name;
                    socket.emit('device:registered', {
                        success: true,
                        deviceId: device.id,
                        message: `设备注册成功: ${device.name}`
                    });
                    this.updateConnectedDevicesCount();
                    console.log(`设备注册成功: ${device.type} - ${device.name} (${device.id})`);
                }
                catch (error) {
                    this.stats.errors++;
                    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                    socket.emit('device:registered', {
                        success: false,
                        deviceId: '',
                        message: errorMessage
                    });
                    console.error('设备注册失败:', errorMessage);
                }
            });
            socket.on('pairing:generate-code', async () => {
                try {
                    const deviceId = socket.data.deviceId;
                    if (!deviceId) {
                        throw new types_1.SmartInputError('Device not registered', 'DEVICE_NOT_REGISTERED', 400);
                    }
                    this.stats.pairingAttempts++;
                    const result = await this.pairingManager.generatePairingCode(deviceId);
                    socket.emit('pairing:code-generated', result);
                    console.log(`配对码生成: ${result.code} for device ${deviceId}`);
                }
                catch (error) {
                    this.stats.errors++;
                    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                    socket.emit('pairing:failed', { message: errorMessage });
                    console.error('配对码生成失败:', errorMessage);
                }
            });
            socket.on('pairing:use-code', async (data) => {
                try {
                    const deviceId = socket.data.deviceId;
                    if (!deviceId) {
                        throw new types_1.SmartInputError('Device not registered', 'DEVICE_NOT_REGISTERED', 400);
                    }
                    const result = await this.pairingManager.usePairingCode(data.code, deviceId);
                    if (result.success) {
                        this.stats.successfulPairings++;
                        const targetDevice = this.deviceManager.getDevice(result.deviceId);
                        if (targetDevice) {
                            this.io.to(targetDevice.socketId).emit('pairing:success', {
                                success: true,
                                deviceId: deviceId,
                                deviceName: socket.data.deviceName || 'Unknown Device'
                            });
                        }
                    }
                    socket.emit('pairing:success', result);
                    console.log(`配对成功: ${deviceId} <-> ${result.deviceId}`);
                }
                catch (error) {
                    this.stats.errors++;
                    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                    socket.emit('pairing:failed', { message: errorMessage });
                    console.error('配对失败:', errorMessage);
                }
            });
            socket.on('voice:send-text', (data) => {
                try {
                    const deviceId = socket.data.deviceId;
                    if (!deviceId) {
                        throw new types_1.SmartInputError('Device not registered', 'DEVICE_NOT_REGISTERED', 400);
                    }
                    const voiceData = {
                        ...data,
                        timestamp: Date.now(),
                        deviceId
                    };
                    const browserDevices = this.deviceManager.getDevicesByType('browser');
                    browserDevices.forEach(device => {
                        if (device.isOnline && device.capabilities.canReceiveText) {
                            this.io.to(device.socketId).emit('voice:text', voiceData);
                        }
                    });
                    this.stats.messagesProcessed++;
                    this.stats.lastActivity = Date.now();
                    this.deviceManager.updateDeviceActivity(deviceId);
                    console.log(`语音文字转发: "${data.text}" from ${deviceId} to ${browserDevices.length} browser(s)`);
                }
                catch (error) {
                    this.stats.errors++;
                    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                    socket.emit('voice:error', { message: errorMessage });
                    console.error('语音文字发送失败:', errorMessage);
                }
            });
            socket.on('device:get-list', () => {
                const devices = this.deviceManager.getOnlineDevices().map(device => ({
                    id: device.id,
                    type: device.type,
                    name: device.name,
                    capabilities: device.capabilities,
                    timestamp: device.timestamp
                }));
                socket.emit('device:list', devices);
            });
            socket.on('system:heartbeat', () => {
                const deviceId = socket.data.deviceId;
                if (deviceId) {
                    this.deviceManager.updateDeviceActivity(deviceId);
                }
                socket.emit('connection:status', {
                    connected: true,
                    timestamp: Date.now()
                });
            });
            socket.on('disconnect', (reason) => {
                console.log(`设备断开连接: ${socket.id}, 原因: ${reason}`);
                if (socket.data.deviceId) {
                    this.deviceManager.unregisterDevice(socket.id);
                    this.pairingManager.clearDevicePairingCodes(socket.data.deviceId);
                }
                this.updateConnectedDevicesCount();
            });
        });
    }
    setupErrorHandlers() {
        process.on('uncaughtException', (error) => {
            console.error('未捕获的异常:', error);
            this.stats.errors++;
        });
        process.on('unhandledRejection', (reason, promise) => {
            console.error('未处理的Promise拒绝:', reason);
            this.stats.errors++;
        });
    }
    updateConnectedDevicesCount() {
        this.stats.connectedDevices = this.deviceManager.getOnlineDevices().length;
    }
    start() {
        this.server.listen(config_1.config.port, config_1.config.host, () => {
            console.log(`SmartInput 服务器启动成功`);
            console.log(`地址: http://${config_1.config.host}:${config_1.config.port}`);
            console.log(`环境: ${process.env.NODE_ENV || 'development'}`);
            console.log(`时间: ${new Date().toISOString()}`);
        });
    }
    stop() {
        return new Promise((resolve) => {
            console.log('正在关闭服务器...');
            this.deviceManager.destroy();
            this.pairingManager.destroy();
            this.server.close(() => {
                console.log('服务器已关闭');
                resolve();
            });
        });
    }
}
const server = new SmartInputServer();
server.start();
const gracefulShutdown = async (signal) => {
    console.log(`收到 ${signal} 信号，正在关闭服务器...`);
    try {
        await server.stop();
        process.exit(0);
    }
    catch (error) {
        console.error('关闭服务器时出错:', error);
        process.exit(1);
    }
};
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
exports.default = SmartInputServer;
