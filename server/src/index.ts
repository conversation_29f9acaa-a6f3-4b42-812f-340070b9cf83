import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import cors from 'cors';
import helmet from 'helmet';
import { config, validateConfig, isDevelopment } from './config';
import { 
  ServerToClientEvents, 
  ClientToServerEvents, 
  InterServerEvents, 
  SocketData,
  DeviceInfo,
  VoiceTextData,
  SmartInputError,
  ApiResponse,
  ServerStats
} from './types';
import DeviceManager = require('./services/DeviceManager');
import PairingManager = require('./services/PairingManager');

class SmartInputServer {
  private app: express.Application;
  private server: ReturnType<typeof createServer>;
  private io: Server<ClientToServerEvents, ServerToClientEvents, InterServerEvents, SocketData>;
  private deviceManager: DeviceManager;
  private pairingManager: PairingManager;
  private stats: ServerStats;
  private startTime: number;

  constructor() {
    // 验证配置
    validateConfig();

    this.app = express();
    this.server = createServer(this.app);
    this.io = new Server<ClientToServerEvents, ServerToClientEvents, InterServerEvents, SocketData>(
      this.server, 
      { cors: config.cors }
    );

    this.deviceManager = new DeviceManager();
    this.pairingManager = new PairingManager(this.deviceManager);
    this.startTime = Date.now();
    this.stats = this.initializeStats();

    this.setupMiddleware();
    this.setupRoutes();
    this.setupSocketHandlers();
    this.setupErrorHandlers();
  }

  private initializeStats(): ServerStats {
    return {
      uptime: 0,
      connectedDevices: 0,
      totalConnections: 0,
      messagesProcessed: 0,
      pairingAttempts: 0,
      successfulPairings: 0,
      errors: 0,
      lastActivity: Date.now()
    };
  }

  private setupMiddleware(): void {
    if (!isDevelopment) {
      this.app.use(helmet());
    }
    
    this.app.use(cors(config.cors));
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));
    
    // 请求日志
    this.app.use((req, res, next) => {
      console.log(`${new Date().toISOString()} ${req.method} ${req.path}`);
      next();
    });
  }

  private setupRoutes(): void {
    // 健康检查
    this.app.get('/health', (req, res) => {
      const response: ApiResponse = {
        success: true,
        data: {
          status: 'ok',
          timestamp: new Date().toISOString(),
          uptime: Date.now() - this.startTime,
          connectedDevices: this.deviceManager.getDeviceCount(),
          version: '1.0.0'
        },
        timestamp: Date.now()
      };
      res.json(response);
    });

    // 获取服务器统计信息
    this.app.get('/api/stats', (req, res) => {
      const deviceStats = this.deviceManager.getDevicesStatus();
      const pairingStats = this.pairingManager.getStats();

      const response: ApiResponse = {
        success: true,
        data: {
          server: {
            ...this.stats,
            uptime: Date.now() - this.startTime
          },
          devices: deviceStats,
          pairing: pairingStats
        },
        timestamp: Date.now()
      };
      res.json(response);
    });

    // 获取设备列表
    this.app.get('/api/devices', (req, res) => {
      const devices = this.deviceManager.getOnlineDevices().map(device => ({
        id: device.id,
        type: device.type,
        name: device.name,
        capabilities: device.capabilities,
        isOnline: device.isOnline,
        lastSeen: device.lastSeen
      }));

      const response: ApiResponse = {
        success: true,
        data: devices,
        timestamp: Date.now()
      };
      res.json(response);
    });

    // 错误处理
    this.app.use('*', (req, res) => {
      const response: ApiResponse = {
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: `Route ${req.originalUrl} not found`,
          timestamp: Date.now()
        }
      };
      res.status(404).json(response);
    });
  }

  private setupSocketHandlers(): void {
    this.io.on('connection', (socket) => {
      console.log(`新设备连接: ${socket.id}`);
      this.stats.totalConnections++;
      this.updateConnectedDevicesCount();

      // 设备注册
      socket.on('device:register', async (deviceInfo: Omit<DeviceInfo, 'id' | 'timestamp'>) => {
        try {
          const device = this.deviceManager.registerDevice(socket.id, deviceInfo);
          socket.data.deviceId = device.id;
          socket.data.deviceType = device.type;
          socket.data.deviceName = device.name;

          socket.emit('device:registered', {
            success: true,
            deviceId: device.id,
            message: `设备注册成功: ${device.name}`
          });

          this.updateConnectedDevicesCount();
          console.log(`设备注册成功: ${device.type} - ${device.name} (${device.id})`);
        } catch (error) {
          this.stats.errors++;
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          socket.emit('device:registered', {
            success: false,
            deviceId: '',
            message: errorMessage
          });
          console.error('设备注册失败:', errorMessage);
        }
      });

      // 生成配对码
      socket.on('pairing:generate-code', async () => {
        try {
          const deviceId = socket.data.deviceId;
          if (!deviceId) {
            throw new SmartInputError('Device not registered', 'DEVICE_NOT_REGISTERED', 400);
          }

          this.stats.pairingAttempts++;
          const result = await this.pairingManager.generatePairingCode(deviceId);
          
          socket.emit('pairing:code-generated', result);
          console.log(`配对码生成: ${result.code} for device ${deviceId}`);
        } catch (error) {
          this.stats.errors++;
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          socket.emit('pairing:failed', { message: errorMessage });
          console.error('配对码生成失败:', errorMessage);
        }
      });

      // 使用配对码
      socket.on('pairing:use-code', async (data: { code: string }) => {
        try {
          const deviceId = socket.data.deviceId;
          if (!deviceId) {
            throw new SmartInputError('Device not registered', 'DEVICE_NOT_REGISTERED', 400);
          }

          const result = await this.pairingManager.usePairingCode(data.code, deviceId);
          
          if (result.success) {
            this.stats.successfulPairings++;
            
            // 通知目标设备配对成功
            const targetDevice = this.deviceManager.getDevice(result.deviceId);
            if (targetDevice) {
              this.io.to(targetDevice.socketId).emit('pairing:success', {
                success: true,
                deviceId: deviceId,
                deviceName: socket.data.deviceName || 'Unknown Device'
              });
            }
          }

          socket.emit('pairing:success', result);
          console.log(`配对成功: ${deviceId} <-> ${result.deviceId}`);
        } catch (error) {
          this.stats.errors++;
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          socket.emit('pairing:failed', { message: errorMessage });
          console.error('配对失败:', errorMessage);
        }
      });

      // 语音文字发送
      socket.on('voice:send-text', (data: Omit<VoiceTextData, 'timestamp'>) => {
        try {
          const deviceId = socket.data.deviceId;
          if (!deviceId) {
            throw new SmartInputError('Device not registered', 'DEVICE_NOT_REGISTERED', 400);
          }

          const voiceData: VoiceTextData = {
            ...data,
            timestamp: Date.now(),
            deviceId
          };

          // 转发到所有浏览器设备
          const browserDevices = this.deviceManager.getDevicesByType('browser');
          browserDevices.forEach(device => {
            if (device.isOnline && device.capabilities.canReceiveText) {
              this.io.to(device.socketId).emit('voice:text', voiceData);
            }
          });

          this.stats.messagesProcessed++;
          this.stats.lastActivity = Date.now();
          this.deviceManager.updateDeviceActivity(deviceId);
          
          console.log(`语音文字转发: "${data.text}" from ${deviceId} to ${browserDevices.length} browser(s)`);
        } catch (error) {
          this.stats.errors++;
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          socket.emit('voice:error', { message: errorMessage });
          console.error('语音文字发送失败:', errorMessage);
        }
      });

      // 设备列表请求
      socket.on('device:get-list', () => {
        const devices = this.deviceManager.getOnlineDevices().map(device => ({
          id: device.id,
          type: device.type,
          name: device.name,
          capabilities: device.capabilities,
          timestamp: device.timestamp
        }));
        socket.emit('device:list', devices);
      });

      // 心跳检测
      socket.on('system:heartbeat', () => {
        const deviceId = socket.data.deviceId;
        if (deviceId) {
          this.deviceManager.updateDeviceActivity(deviceId);
        }
        socket.emit('connection:status', {
          connected: true,
          timestamp: Date.now()
        });
      });

      // 断开连接
      socket.on('disconnect', (reason) => {
        console.log(`设备断开连接: ${socket.id}, 原因: ${reason}`);
        
        if (socket.data.deviceId) {
          this.deviceManager.unregisterDevice(socket.id);
          this.pairingManager.clearDevicePairingCodes(socket.data.deviceId);
        }
        
        this.updateConnectedDevicesCount();
      });
    });
  }

  private setupErrorHandlers(): void {
    // 全局错误处理
    process.on('uncaughtException', (error) => {
      console.error('未捕获的异常:', error);
      this.stats.errors++;
    });

    process.on('unhandledRejection', (reason, promise) => {
      console.error('未处理的Promise拒绝:', reason);
      this.stats.errors++;
    });
  }

  private updateConnectedDevicesCount(): void {
    this.stats.connectedDevices = this.deviceManager.getOnlineDevices().length;
  }

  public start(): void {
    this.server.listen(config.port, config.host, () => {
      console.log(`SmartInput 服务器启动成功`);
      console.log(`地址: http://${config.host}:${config.port}`);
      console.log(`环境: ${process.env.NODE_ENV || 'development'}`);
      console.log(`时间: ${new Date().toISOString()}`);
    });
  }

  public stop(): Promise<void> {
    return new Promise((resolve) => {
      console.log('正在关闭服务器...');
      
      // 清理资源
      this.deviceManager.destroy();
      this.pairingManager.destroy();
      
      this.server.close(() => {
        console.log('服务器已关闭');
        resolve();
      });
    });
  }
}

// 启动服务器
const server = new SmartInputServer();
server.start();

// 优雅关闭
const gracefulShutdown = async (signal: string) => {
  console.log(`收到 ${signal} 信号，正在关闭服务器...`);
  try {
    await server.stop();
    process.exit(0);
  } catch (error) {
    console.error('关闭服务器时出错:', error);
    process.exit(1);
  }
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

export default SmartInputServer;
