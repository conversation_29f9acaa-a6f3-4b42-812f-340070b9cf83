import QRCode from 'qrcode';
import { 
  PairingCode, 
  PairingRequest, 
  PairingData,
  SmartInputError,
  LogEntry,
  LogLevel 
} from '../types';
import { config } from '../config';
import { DeviceManager } from './DeviceManager';

class PairingManager {
  private pairingCodes: Map<string, PairingCode> = new Map();
  private pairingAttempts: Map<string, number> = new Map();
  private cleanupInterval: NodeJS.Timeout;

  constructor(private deviceManager: DeviceManager) {
    // 定期清理过期的配对码
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredCodes();
    }, config.cleanup.pairingCodeCleanup);
  }

  /**
   * 生成配对码
   */
  async generatePairingCode(deviceId: string): Promise<{ code: string; qrCodeUrl: string; expiresAt: number }> {
    try {
      // 检查设备是否存在
      const device = this.deviceManager.getDevice(deviceId);
      if (!device) {
        throw new SmartInputError('Device not found', 'DEVICE_NOT_FOUND', 404);
      }

      // 生成配对码
      const code = this.generateCode();
      const expiresAt = Date.now() + config.pairing.expirationTime;

      // 如果设备已有配对码，先清理
      this.clearDevicePairingCodes(deviceId);

      const pairingCode: PairingCode = {
        code,
        deviceId,
        expiresAt,
        isUsed: false
      };

      this.pairingCodes.set(code, pairingCode);

      // 生成QR码
      const qrData = JSON.stringify({
        type: 'smartinput-pairing',
        code,
        deviceId,
        deviceName: device.name,
        expiresAt
      });

      const qrCodeUrl = await QRCode.toDataURL(qrData, {
        errorCorrectionLevel: 'M' as const,
        margin: 1,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        },
        width: 256
      });

      this.log('info', `配对码生成成功: ${code} for device ${deviceId}`, {
        deviceId,
        code,
        expiresAt
      });

      return { code, qrCodeUrl, expiresAt };
    } catch (error) {
      this.log('error', `配对码生成失败: ${error instanceof Error ? error.message : 'Unknown error'}`, {
        deviceId
      });
      throw error;
    }
  }

  /**
   * 使用配对码进行配对
   */
  async usePairingCode(code: string, requestingDeviceId: string): Promise<PairingData> {
    try {
      // 检查配对码是否存在
      const pairingCode = this.pairingCodes.get(code);
      if (!pairingCode) {
        throw new SmartInputError('Invalid pairing code', 'INVALID_PAIRING_CODE', 400);
      }

      // 检查配对码是否过期
      if (Date.now() > pairingCode.expiresAt) {
        this.pairingCodes.delete(code);
        throw new SmartInputError('Pairing code expired', 'PAIRING_CODE_EXPIRED', 400);
      }

      // 检查配对码是否已使用
      if (pairingCode.isUsed) {
        throw new SmartInputError('Pairing code already used', 'PAIRING_CODE_USED', 400);
      }

      // 检查请求设备是否存在
      const requestingDevice = this.deviceManager.getDevice(requestingDeviceId);
      if (!requestingDevice) {
        throw new SmartInputError('Requesting device not found', 'REQUESTING_DEVICE_NOT_FOUND', 404);
      }

      // 检查目标设备是否存在
      const targetDevice = this.deviceManager.getDevice(pairingCode.deviceId);
      if (!targetDevice) {
        this.pairingCodes.delete(code);
        throw new SmartInputError('Target device not found', 'TARGET_DEVICE_NOT_FOUND', 404);
      }

      // 检查设备是否在线
      if (!this.deviceManager.isDeviceOnline(pairingCode.deviceId)) {
        throw new SmartInputError('Target device is offline', 'TARGET_DEVICE_OFFLINE', 400);
      }

      // 检查配对尝试次数
      const attemptKey = `${requestingDeviceId}:${code}`;
      const attempts = this.pairingAttempts.get(attemptKey) || 0;
      if (attempts >= config.pairing.maxAttempts) {
        throw new SmartInputError('Too many pairing attempts', 'TOO_MANY_ATTEMPTS', 429);
      }

      // 增加尝试次数
      this.pairingAttempts.set(attemptKey, attempts + 1);

      // 标记配对码为已使用
      pairingCode.isUsed = true;

      // 清理相关的配对尝试记录
      this.cleanupPairingAttempts(requestingDeviceId);

      const pairingData: PairingData = {
        success: true,
        deviceId: targetDevice.id,
        deviceName: targetDevice.name
      };

      this.log('info', `配对成功: ${requestingDevice.name} (${requestingDeviceId}) <-> ${targetDevice.name} (${targetDevice.id})`, {
        requestingDeviceId,
        targetDeviceId: targetDevice.id,
        code
      });

      return pairingData;
    } catch (error) {
      this.log('error', `配对失败: ${error instanceof Error ? error.message : 'Unknown error'}`, {
        code,
        requestingDeviceId
      });
      throw error;
    }
  }

  /**
   * 验证配对码
   */
  validatePairingCode(code: string): boolean {
    const pairingCode = this.pairingCodes.get(code);
    if (!pairingCode) {
      return false;
    }

    if (Date.now() > pairingCode.expiresAt || pairingCode.isUsed) {
      this.pairingCodes.delete(code);
      return false;
    }

    return true;
  }

  /**
   * 获取配对码信息
   */
  getPairingCodeInfo(code: string): PairingCode | undefined {
    const pairingCode = this.pairingCodes.get(code);
    if (!pairingCode) {
      return undefined;
    }

    // 检查是否过期
    if (Date.now() > pairingCode.expiresAt) {
      this.pairingCodes.delete(code);
      return undefined;
    }

    return pairingCode;
  }

  /**
   * 清理设备的所有配对码
   */
  clearDevicePairingCodes(deviceId: string): void {
    const codesToDelete: string[] = [];
    
    for (const [code, pairingCode] of this.pairingCodes.entries()) {
      if (pairingCode.deviceId === deviceId) {
        codesToDelete.push(code);
      }
    }

    for (const code of codesToDelete) {
      this.pairingCodes.delete(code);
    }

    if (codesToDelete.length > 0) {
      this.log('debug', `清理设备配对码: ${deviceId}, 清理数量: ${codesToDelete.length}`);
    }
  }

  /**
   * 获取统计信息
   */
  getStats() {
    const now = Date.now();
    const activeCodes = Array.from(this.pairingCodes.values()).filter(
      code => !code.isUsed && code.expiresAt > now
    );

    return {
      totalCodes: this.pairingCodes.size,
      activeCodes: activeCodes.length,
      usedCodes: Array.from(this.pairingCodes.values()).filter(code => code.isUsed).length,
      expiredCodes: Array.from(this.pairingCodes.values()).filter(
        code => !code.isUsed && code.expiresAt <= now
      ).length
    };
  }

  /**
   * 生成随机配对码
   */
  private generateCode(): string {
    const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let result = '';
    
    for (let i = 0; i < config.pairing.codeLength; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    // 确保生成的码是唯一的
    if (this.pairingCodes.has(result)) {
      return this.generateCode();
    }
    
    return result;
  }

  /**
   * 清理过期的配对码
   */
  private cleanupExpiredCodes(): void {
    const now = Date.now();
    const codesToDelete: string[] = [];

    for (const [code, pairingCode] of this.pairingCodes.entries()) {
      if (now > pairingCode.expiresAt) {
        codesToDelete.push(code);
      }
    }

    for (const code of codesToDelete) {
      this.pairingCodes.delete(code);
    }

    if (codesToDelete.length > 0) {
      this.log('debug', `清理过期配对码: ${codesToDelete.length} 个`);
    }
  }

  /**
   * 清理配对尝试记录
   */
  private cleanupPairingAttempts(deviceId: string): void {
    const keysToDelete: string[] = [];
    
    for (const key of this.pairingAttempts.keys()) {
      if (key.startsWith(`${deviceId}:`)) {
        keysToDelete.push(key);
      }
    }

    for (const key of keysToDelete) {
      this.pairingAttempts.delete(key);
    }
  }

  /**
   * 日志记录
   */
  private log(level: LogLevel, message: string, metadata?: Record<string, any>): void {
    const logEntry: LogEntry = {
      level,
      message,
      timestamp: Date.now(),
      metadata: metadata || undefined
    };

    console.log(`[${level.toUpperCase()}] ${message}`, metadata ? JSON.stringify(metadata) : '');
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.pairingCodes.clear();
    this.pairingAttempts.clear();
  }
}

export = PairingManager;
