import { v4 as uuidv4 } from 'uuid';
import { 
  DeviceInfo, 
  RegisteredDevice, 
  DeviceType, 
  DeviceCapabilities,
  SmartInputError,
  LogEntry,
  LogLevel 
} from '../types';
import { config } from '../config';

class DeviceManager {
  private devices: Map<string, RegisteredDevice> = new Map();
  private socketToDevice: Map<string, string> = new Map();
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    // 定期清理离线设备
    this.cleanupInterval = setInterval(() => {
      this.cleanupOfflineDevices();
    }, config.cleanup.deviceTimeout);
  }

  /**
   * 注册新设备
   */
  registerDevice(
    socketId: string, 
    deviceInfo: Omit<DeviceInfo, 'id' | 'timestamp'>
  ): RegisteredDevice {
    try {
      const deviceId = this.generateDeviceId(deviceInfo.type, deviceInfo.name);
      const timestamp = Date.now();

      const registeredDevice: RegisteredDevice = {
        ...deviceInfo,
        id: deviceId,
        timestamp,
        socketId,
        lastSeen: timestamp,
        isOnline: true
      };

      // 验证设备信息
      this.validateDeviceInfo(registeredDevice);

      // 如果设备已存在，更新信息
      if (this.devices.has(deviceId)) {
        const existingDevice = this.devices.get(deviceId)!;
        registeredDevice.timestamp = existingDevice.timestamp; // 保持原始注册时间
      }

      this.devices.set(deviceId, registeredDevice);
      this.socketToDevice.set(socketId, deviceId);

      this.log('info', `设备注册成功: ${deviceInfo.type} - ${deviceInfo.name} (${deviceId})`, {
        deviceId,
        socketId,
        deviceType: deviceInfo.type
      });

      return registeredDevice;
    } catch (error) {
      this.log('error', `设备注册失败: ${error instanceof Error ? error.message : 'Unknown error'}`, {
        socketId,
        deviceInfo
      });
      throw error;
    }
  }

  /**
   * 注销设备
   */
  unregisterDevice(socketId: string): boolean {
    const deviceId = this.socketToDevice.get(socketId);
    if (!deviceId) {
      return false;
    }

    const device = this.devices.get(deviceId);
    if (device) {
      device.isOnline = false;
      device.lastSeen = Date.now();
      
      this.log('info', `设备断开连接: ${device.type} - ${device.name} (${deviceId})`, {
        deviceId,
        socketId,
        deviceType: device.type
      });
    }

    this.socketToDevice.delete(socketId);
    return true;
  }

  /**
   * 获取设备信息
   */
  getDevice(deviceId: string): RegisteredDevice | undefined {
    return this.devices.get(deviceId);
  }

  /**
   * 通过Socket ID获取设备
   */
  getDeviceBySocketId(socketId: string): RegisteredDevice | undefined {
    const deviceId = this.socketToDevice.get(socketId);
    return deviceId ? this.devices.get(deviceId) : undefined;
  }

  /**
   * 获取所有在线设备
   */
  getOnlineDevices(): RegisteredDevice[] {
    return Array.from(this.devices.values()).filter(device => device.isOnline);
  }

  /**
   * 获取所有设备
   */
  getAllDevices(): RegisteredDevice[] {
    return Array.from(this.devices.values());
  }

  /**
   * 获取特定类型的设备
   */
  getDevicesByType(type: DeviceType): RegisteredDevice[] {
    return Array.from(this.devices.values()).filter(device => device.type === type);
  }

  /**
   * 获取具有特定能力的设备
   */
  getDevicesByCapability(capability: keyof DeviceCapabilities): RegisteredDevice[] {
    return Array.from(this.devices.values()).filter(device => device.capabilities[capability]);
  }

  /**
   * 更新设备最后活跃时间
   */
  updateDeviceActivity(deviceId: string): void {
    const device = this.devices.get(deviceId);
    if (device) {
      device.lastSeen = Date.now();
    }
  }

  /**
   * 检查设备是否在线
   */
  isDeviceOnline(deviceId: string): boolean {
    const device = this.devices.get(deviceId);
    return device ? device.isOnline : false;
  }

  /**
   * 获取设备统计信息
   */
  getStats() {
    const allDevices = this.getAllDevices();
    const onlineDevices = this.getOnlineDevices();

    return {
      totalDevices: allDevices.length,
      onlineDevices: onlineDevices.length,
      devicesByType: {
        mobile: allDevices.filter(d => d.type === 'mobile').length,
        browser: allDevices.filter(d => d.type === 'browser').length,
        desktop: allDevices.filter(d => d.type === 'desktop').length
      },
      onlineByType: {
        mobile: onlineDevices.filter(d => d.type === 'mobile').length,
        browser: onlineDevices.filter(d => d.type === 'browser').length,
        desktop: onlineDevices.filter(d => d.type === 'desktop').length
      }
    };
  }

  /**
   * 获取设备数量 (兼容方法)
   */
  getDeviceCount(): number {
    return this.getOnlineDevices().length;
  }

  /**
   * 获取设备状态统计 (兼容方法)
   */
  getDevicesStatus() {
    const devices = this.getAllDevices();
    const onlineDevices = this.getOnlineDevices();

    return {
      total: devices.length,
      online: onlineDevices.length,
      byType: {
        mobile: devices.filter(d => d.type === 'mobile').length,
        browser: devices.filter(d => d.type === 'browser').length,
        desktop: devices.filter(d => d.type === 'desktop').length
      },
      onlineByType: {
        mobile: onlineDevices.filter(d => d.type === 'mobile').length,
        browser: onlineDevices.filter(d => d.type === 'browser').length,
        desktop: onlineDevices.filter(d => d.type === 'desktop').length
      },
      sessions: 0 // 暂时设为0，因为当前实现中没有会话概念
    };
  }

  /**
   * 清理离线设备
   */
  private cleanupOfflineDevices(): void {
    const now = Date.now();
    const timeout = config.cleanup.deviceTimeout;
    let cleanedCount = 0;

    for (const [deviceId, device] of this.devices.entries()) {
      if (!device.isOnline && (now - device.lastSeen) > timeout) {
        this.devices.delete(deviceId);
        cleanedCount++;
        
        this.log('debug', `清理离线设备: ${device.type} - ${device.name} (${deviceId})`, {
          deviceId,
          lastSeen: device.lastSeen,
          offlineTime: now - device.lastSeen
        });
      }
    }

    if (cleanedCount > 0) {
      this.log('info', `清理了 ${cleanedCount} 个离线设备`);
    }
  }

  /**
   * 生成设备ID
   */
  private generateDeviceId(type: DeviceType, name: string): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 8);
    return `${type}_${name.toLowerCase().replace(/[^a-z0-9]/g, '')}_${timestamp}${random}`;
  }

  /**
   * 验证设备信息
   */
  private validateDeviceInfo(device: RegisteredDevice): void {
    if (!device.id || typeof device.id !== 'string') {
      throw new SmartInputError('Invalid device ID', 'INVALID_DEVICE_ID', 400);
    }

    if (!device.type || !['mobile', 'browser', 'desktop'].includes(device.type)) {
      throw new SmartInputError('Invalid device type', 'INVALID_DEVICE_TYPE', 400);
    }

    if (!device.name || typeof device.name !== 'string' || device.name.trim().length === 0) {
      throw new SmartInputError('Invalid device name', 'INVALID_DEVICE_NAME', 400);
    }

    if (!device.capabilities || typeof device.capabilities !== 'object') {
      throw new SmartInputError('Invalid device capabilities', 'INVALID_CAPABILITIES', 400);
    }

    // 验证能力配置的合理性
    const { capabilities } = device;
    if (device.type === 'mobile' && !capabilities.canSendVoice) {
      throw new SmartInputError('Mobile devices must support voice sending', 'INVALID_MOBILE_CAPABILITIES', 400);
    }

    if (device.type === 'browser' && !capabilities.canReceiveText) {
      throw new SmartInputError('Browser devices must support text receiving', 'INVALID_BROWSER_CAPABILITIES', 400);
    }
  }

  /**
   * 日志记录
   */
  private log(level: LogLevel, message: string, metadata?: Record<string, any>): void {
    const logEntry: LogEntry = {
      level,
      message,
      timestamp: Date.now(),
      metadata: metadata || undefined
    };

    // 这里可以集成更复杂的日志系统
    console.log(`[${level.toUpperCase()}] ${message}`, metadata ? JSON.stringify(metadata) : '');
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.devices.clear();
    this.socketToDevice.clear();
  }
}

export = DeviceManager;
