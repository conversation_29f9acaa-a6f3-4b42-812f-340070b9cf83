{"name": "rate-limiter-flexible", "version": "2.4.2", "description": "Node.js rate limiter by key and protection from DDoS and Brute-Force attacks in process Memory, Redis, MongoDb, Memcached, MySQL, PostgreSQL, Cluster or PM", "main": "index.js", "scripts": {"test": "istanbul -v cover -- _mocha --recursive", "debug-test": "mocha --inspect-brk lib/**/**.test.js", "coveralls": "cat ./coverage/lcov.info | coveralls", "eslint": "eslint --quiet lib/**/**.js test/**/**.js", "eslint-fix": "eslint --fix lib/**/**.js test/**/**.js"}, "repository": {"type": "git", "url": "git+https://github.com/animir/node-rate-limiter-flexible.git"}, "keywords": ["authorization", "security", "rate", "limit", "ratelimter", "brute", "force", "bruteforce", "throttle", "koa", "express", "hapi", "auth", "ddos", "queue"], "author": "animir <<EMAIL>>", "license": "ISC", "bugs": {"url": "https://github.com/animir/node-rate-limiter-flexible/issues"}, "homepage": "https://github.com/animir/node-rate-limiter-flexible#readme", "types": "./lib/index.d.ts", "devDependencies": {"chai": "^4.1.2", "coveralls": "^3.0.1", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.7.0", "eslint-plugin-node": "^6.0.1", "eslint-plugin-security": "^1.4.0", "istanbul": "^0.4.5", "memcached-mock": "^0.1.0", "mocha": "^5.1.1", "redis-mock": "^0.48.0", "sinon": "^5.0.10"}, "browser": {"cluster": false, "crypto": false}}