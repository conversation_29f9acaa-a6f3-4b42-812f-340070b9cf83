<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartInput 测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .test-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section h2 {
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .form-group textarea {
            min-height: 120px;
            resize: vertical;
        }
        
        .contenteditable {
            min-height: 100px;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            background: white;
            font-size: 16px;
        }
        
        .contenteditable:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .contenteditable:empty:before {
            content: attr(data-placeholder);
            color: #999;
        }
        
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .instructions h3 {
            color: #1976d2;
            margin-bottom: 15px;
        }
        
        .instructions ol {
            margin-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
        }
        
        .status-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #f44336;
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            z-index: 1000;
        }
        
        .status-indicator.connected {
            background: #4CAF50;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .demo-card {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }
        
        .demo-card h4 {
            margin-bottom: 15px;
            color: #333;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .result-display {
            margin-top: 20px;
            padding: 15px;
            background: #f0f8ff;
            border-radius: 6px;
            border-left: 4px solid #2196F3;
        }
        
        .result-display h4 {
            margin-bottom: 10px;
            color: #1976d2;
        }
        
        .result-content {
            font-family: 'Courier New', monospace;
            background: white;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ddd;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎤 SmartInput 测试页面</h1>
            <p>测试语音输入功能和浏览器扩展</p>
        </div>
        
        <div class="status-indicator" id="statusIndicator">
            扩展未连接
        </div>
        
        <div class="instructions">
            <h3>📋 测试说明</h3>
            <ol>
                <li>确保SmartInput服务器正在运行 (localhost:3001)</li>
                <li>确保移动端应用正在运行 (localhost:3000)</li>
                <li>安装并启用SmartInput浏览器扩展</li>
                <li>点击扩展图标，开始配对移动设备</li>
                <li>点击下方任意输入框，然后在手机上进行语音输入</li>
                <li>观察文字是否自动填充到输入框中</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h2>🔤 基础文本输入测试</h2>
            <div class="demo-grid">
                <div class="demo-card">
                    <h4>普通文本框</h4>
                    <div class="form-group">
                        <label for="textInput">文本输入:</label>
                        <input type="text" id="textInput" placeholder="点击这里，然后使用语音输入...">
                    </div>
                </div>
                
                <div class="demo-card">
                    <h4>搜索框</h4>
                    <div class="form-group">
                        <label for="searchInput">搜索:</label>
                        <input type="search" id="searchInput" placeholder="搜索内容...">
                    </div>
                </div>
                
                <div class="demo-card">
                    <h4>邮箱输入</h4>
                    <div class="form-group">
                        <label for="emailInput">邮箱:</label>
                        <input type="email" id="emailInput" placeholder="<EMAIL>">
                    </div>
                </div>
                
                <div class="demo-card">
                    <h4>URL输入</h4>
                    <div class="form-group">
                        <label for="urlInput">网址:</label>
                        <input type="url" id="urlInput" placeholder="https://example.com">
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📝 多行文本测试</h2>
            <div class="form-group">
                <label for="textareaInput">多行文本框:</label>
                <textarea id="textareaInput" placeholder="这是一个多行文本框，可以输入较长的内容..."></textarea>
            </div>
        </div>
        
        <div class="test-section">
            <h2>✏️ 可编辑元素测试</h2>
            <div class="form-group">
                <label>富文本编辑器 (contenteditable):</label>
                <div class="contenteditable" contenteditable="true" data-placeholder="这是一个可编辑的div元素，支持富文本编辑..."></div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🎯 表单测试</h2>
            <form id="testForm">
                <div class="demo-grid">
                    <div class="form-group">
                        <label for="nameInput">姓名:</label>
                        <input type="text" id="nameInput" name="name" placeholder="请输入您的姓名">
                    </div>
                    
                    <div class="form-group">
                        <label for="phoneInput">电话:</label>
                        <input type="tel" id="phoneInput" name="phone" placeholder="请输入电话号码">
                    </div>
                    
                    <div class="form-group">
                        <label for="companyInput">公司:</label>
                        <input type="text" id="companyInput" name="company" placeholder="请输入公司名称">
                    </div>
                    
                    <div class="form-group">
                        <label for="positionInput">职位:</label>
                        <input type="text" id="positionInput" name="position" placeholder="请输入职位">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="messageInput">留言:</label>
                    <textarea id="messageInput" name="message" placeholder="请输入您的留言或建议..."></textarea>
                </div>
                
                <button type="button" class="btn" onclick="showFormData()">显示表单数据</button>
            </form>
        </div>
        
        <div class="result-display" id="resultDisplay" style="display: none;">
            <h4>📊 表单数据结果</h4>
            <div class="result-content" id="resultContent"></div>
        </div>
    </div>
    
    <script>
        // 检查扩展状态
        function checkExtensionStatus() {
            const indicator = document.getElementById('statusIndicator');
            
            // 尝试检测扩展是否已安装
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                try {
                    // 这里可以添加与扩展通信的代码
                    indicator.textContent = '扩展已安装';
                    indicator.classList.add('connected');
                } catch (error) {
                    indicator.textContent = '扩展未安装';
                    indicator.classList.remove('connected');
                }
            } else {
                indicator.textContent = '扩展未安装';
                indicator.classList.remove('connected');
            }
        }
        
        // 显示表单数据
        function showFormData() {
            const form = document.getElementById('testForm');
            const formData = new FormData(form);
            const data = {};
            
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            const resultDisplay = document.getElementById('resultDisplay');
            const resultContent = document.getElementById('resultContent');
            
            resultContent.textContent = JSON.stringify(data, null, 2);
            resultDisplay.style.display = 'block';
            
            // 滚动到结果区域
            resultDisplay.scrollIntoView({ behavior: 'smooth' });
        }
        
        // 监听输入事件，显示实时反馈
        document.addEventListener('input', function(event) {
            if (event.target.matches('input, textarea, [contenteditable]')) {
                console.log('输入检测:', {
                    element: event.target.tagName,
                    type: event.target.type || 'contenteditable',
                    value: event.target.value || event.target.textContent,
                    timestamp: new Date().toISOString()
                });
            }
        });
        
        // 监听焦点事件
        document.addEventListener('focusin', function(event) {
            if (event.target.matches('input, textarea, [contenteditable]')) {
                console.log('输入框获得焦点:', {
                    element: event.target.tagName,
                    id: event.target.id,
                    type: event.target.type || 'contenteditable'
                });
                
                // 添加视觉反馈
                event.target.style.boxShadow = '0 0 0 3px rgba(102, 126, 234, 0.3)';
            }
        });
        
        document.addEventListener('focusout', function(event) {
            if (event.target.matches('input, textarea, [contenteditable]')) {
                // 移除视觉反馈
                event.target.style.boxShadow = '';
            }
        });
        
        // 页面加载时检查扩展状态
        document.addEventListener('DOMContentLoaded', function() {
            checkExtensionStatus();
            
            // 定期检查扩展状态
            setInterval(checkExtensionStatus, 5000);
        });
        
        // 添加键盘快捷键提示
        document.addEventListener('keydown', function(event) {
            if (event.ctrlKey && event.key === 'i') {
                event.preventDefault();
                alert('SmartInput 快捷键:\n- 点击任意输入框\n- 使用手机进行语音输入\n- 文字将自动填充');
            }
        });
        
        console.log('SmartInput 测试页面已加载');
        console.log('使用 Ctrl+I 查看快捷键帮助');
    </script>
</body>
</html>
