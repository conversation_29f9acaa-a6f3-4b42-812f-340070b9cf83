# SmartInput 浏览器扩展

SmartInput 浏览器扩展允许您通过手机语音输入，自动填充网页中的文本输入框。

## 功能特性

- 🎤 **语音输入**: 通过手机应用进行语音识别
- 🔗 **设备配对**: QR码扫描快速配对移动设备
- 📝 **智能填充**: 自动检测并填充网页输入框
- 🔄 **实时通信**: WebSocket实时传输语音文字
- 📱 **跨平台**: 支持所有现代浏览器

## 安装方法

### 1. 开发者模式安装

1. **打开Chrome扩展管理页面**
   - 在地址栏输入 `chrome://extensions/`
   - 或者点击菜单 → 更多工具 → 扩展程序

2. **启用开发者模式**
   - 在页面右上角打开"开发者模式"开关

3. **加载扩展**
   - 点击"加载已解压的扩展程序"
   - 选择 `browser-extension` 文件夹
   - 扩展将自动安装并启用

### 2. 准备图标文件

由于需要PNG格式的图标文件，请按以下步骤准备：

1. **在线转换SVG到PNG**
   - 访问 https://convertio.co/svg-png/ 或类似网站
   - 上传 `icons/icon.svg` 文件
   - 分别生成 16x16, 32x32, 48x48, 128x128 像素的PNG文件
   - 将生成的文件重命名为 `icon16.png`, `icon32.png`, `icon48.png`, `icon128.png`
   - 保存到 `icons/` 目录

2. **或者使用命令行工具**（如果系统已安装）
   ```bash
   # 使用 rsvg-convert
   rsvg-convert -w 16 -h 16 icons/icon.svg > icons/icon16.png
   rsvg-convert -w 32 -h 32 icons/icon.svg > icons/icon32.png
   rsvg-convert -w 48 -h 48 icons/icon.svg > icons/icon48.png
   rsvg-convert -w 128 -h 128 icons/icon.svg > icons/icon128.png
   
   # 或使用 ImageMagick
   convert -size 16x16 icons/icon.svg icons/icon16.png
   convert -size 32x32 icons/icon.svg icons/icon32.png
   convert -size 48x48 icons/icon.svg icons/icon48.png
   convert -size 128x128 icons/icon.svg icons/icon128.png
   ```

## 使用方法

### 1. 启动服务器
确保SmartInput服务器正在运行：
```bash
cd server
npm start
```

### 2. 启动移动应用
确保移动端应用正在运行：
```bash
cd mobile-app
npm run dev
```

### 3. 配对设备

1. **点击扩展图标** - 在浏览器工具栏中点击SmartInput图标
2. **开始配对** - 点击"开始配对"按钮生成QR码
3. **扫描配对** - 使用手机SmartInput应用扫描QR码
4. **配对成功** - 扩展会显示配对成功的设备

### 4. 语音输入

1. **点击输入框** - 在网页中点击任意文本输入框
2. **语音输入** - 在手机应用中点击麦克风按钮进行语音输入
3. **自动填充** - 识别的文字会自动填充到输入框中

## 支持的输入框类型

- `<input type="text">` - 文本输入框
- `<input type="search">` - 搜索框
- `<input type="url">` - URL输入框
- `<input type="email">` - 邮箱输入框
- `<textarea>` - 多行文本框
- `<div contenteditable="true">` - 可编辑元素

## 故障排除

### 扩展无法连接服务器
1. 确保SmartInput服务器正在运行（端口3001）
2. 检查防火墙设置
3. 点击"重新连接"按钮

### 无法找到输入框
1. 确保点击了网页中的输入框
2. 检查输入框是否为支持的类型
3. 刷新页面重试

### 配对失败
1. 确保手机和电脑在同一网络
2. 检查移动应用是否正常运行
3. 重新生成配对码

## 开发说明

### 文件结构
```
browser-extension/
├── manifest.json       # 扩展配置文件
├── background.js       # 后台脚本
├── content.js          # 内容脚本
├── content.css         # 内容样式
├── popup.html          # 弹窗页面
├── popup.js            # 弹窗脚本
├── socket.io.js        # Socket.io客户端库
├── icons/              # 图标文件
│   ├── icon.svg        # SVG源文件
│   ├── icon16.png      # 16x16图标
│   ├── icon32.png      # 32x32图标
│   ├── icon48.png      # 48x48图标
│   └── icon128.png     # 128x128图标
└── README.md           # 说明文档
```

### 权限说明
- `activeTab`: 访问当前活动标签页
- `storage`: 存储设备配置信息
- `scripting`: 注入内容脚本
- `host_permissions`: 访问所有网站以检测输入框

## 许可证

MIT License
