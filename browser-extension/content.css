/* SmartInput 内容脚本样式 */

@keyframes smartinput-fade-in {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes smartinput-fade-out {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-10px);
  }
}

@keyframes smartinput-slide-in {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes smartinput-slide-out {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(100%);
  }
}

.smartinput-feedback {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  pointer-events: none;
}

.smartinput-feedback-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.smartinput-text {
  font-weight: 500;
}

.smartinput-confidence {
  font-size: 10px;
  opacity: 0.9;
}

.smartinput-warning {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  cursor: pointer;
}

.smartinput-warning:hover {
  background: #F57C00 !important;
}

.smartinput-warning-content {
  line-height: 1.4;
}

.smartinput-warning-content small {
  opacity: 0.9;
  font-size: 12px;
}

/* 高亮当前活动的输入框 */
.smartinput-active-input {
  outline: 2px solid #4CAF50 !important;
  outline-offset: 1px !important;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2) !important;
}
