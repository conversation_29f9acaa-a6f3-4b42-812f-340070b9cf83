// SmartInput 内容脚本 - 处理网页文字输入

let currentInputElement = null;
let smartInputOverlay = null;

// 监听来自background的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  switch (message.type) {
    case 'INSERT_TEXT':
      insertTextToActiveInput(message.text, message.confidence);
      sendResponse({ success: true });
      break;
      
    default:
      sendResponse({ error: 'Unknown message type' });
  }
  
  return true;
});

// 插入文字到当前活动的输入框
function insertTextToActiveInput(text, confidence = 1.0) {
  const activeElement = document.activeElement;
  let targetElement = activeElement;
  
  // 如果当前没有焦点元素，尝试找到最近使用的输入框
  if (!isInputElement(activeElement)) {
    targetElement = findBestInputElement();
  }
  
  if (targetElement && isInputElement(targetElement)) {
    insertText(targetElement, text);
    showInsertionFeedback(targetElement, text, confidence);
  } else {
    // 如果没有找到输入框，显示提示
    showNoInputWarning(text);
  }
}

// 检查元素是否为输入元素
function isInputElement(element) {
  if (!element) return false;
  
  const tagName = element.tagName.toLowerCase();
  const type = element.type ? element.type.toLowerCase() : '';
  
  // 检查input元素
  if (tagName === 'input') {
    const textTypes = ['text', 'search', 'url', 'email', 'password', 'tel'];
    return textTypes.includes(type) || type === '';
  }
  
  // 检查textarea
  if (tagName === 'textarea') {
    return true;
  }
  
  // 检查可编辑元素
  if (element.contentEditable === 'true') {
    return true;
  }
  
  return false;
}

// 查找最佳的输入元素
function findBestInputElement() {
  // 优先级：最近点击的 > 可见的 > 第一个
  const inputs = document.querySelectorAll('input[type="text"], input[type="search"], input[type="url"], input[type="email"], input:not([type]), textarea, [contenteditable="true"]');
  
  let bestInput = null;
  let maxScore = 0;
  
  inputs.forEach(input => {
    let score = 0;
    
    // 可见性检查
    const rect = input.getBoundingClientRect();
    if (rect.width > 0 && rect.height > 0) {
      score += 10;
    }
    
    // 是否在视口内
    if (rect.top >= 0 && rect.left >= 0 && 
        rect.bottom <= window.innerHeight && 
        rect.right <= window.innerWidth) {
      score += 5;
    }
    
    // 是否有焦点
    if (input === document.activeElement) {
      score += 20;
    }
    
    // 是否最近被点击过
    if (input.dataset.smartInputLastUsed) {
      const lastUsed = parseInt(input.dataset.smartInputLastUsed);
      const timeDiff = Date.now() - lastUsed;
      if (timeDiff < 30000) { // 30秒内
        score += 15;
      }
    }
    
    if (score > maxScore) {
      maxScore = score;
      bestInput = input;
    }
  });
  
  return bestInput;
}

// 插入文字到指定元素
function insertText(element, text) {
  // 记录使用时间
  element.dataset.smartInputLastUsed = Date.now().toString();
  
  if (element.contentEditable === 'true') {
    // 处理可编辑元素
    insertTextToContentEditable(element, text);
  } else {
    // 处理input和textarea
    insertTextToInputElement(element, text);
  }
  
  // 触发输入事件
  element.dispatchEvent(new Event('input', { bubbles: true }));
  element.dispatchEvent(new Event('change', { bubbles: true }));
}

// 插入文字到input/textarea元素
function insertTextToInputElement(element, text) {
  const start = element.selectionStart || 0;
  const end = element.selectionEnd || 0;
  const currentValue = element.value || '';
  
  const newValue = currentValue.substring(0, start) + text + currentValue.substring(end);
  element.value = newValue;
  
  // 设置光标位置
  const newCursorPos = start + text.length;
  element.setSelectionRange(newCursorPos, newCursorPos);
  element.focus();
}

// 插入文字到可编辑元素
function insertTextToContentEditable(element, text) {
  element.focus();
  
  const selection = window.getSelection();
  const range = selection.getRangeAt(0);
  
  range.deleteContents();
  range.insertNode(document.createTextNode(text));
  
  // 移动光标到文字末尾
  range.setStartAfter(range.endContainer);
  range.setEndAfter(range.endContainer);
  selection.removeAllRanges();
  selection.addRange(range);
}

// 显示插入反馈
function showInsertionFeedback(element, text, confidence) {
  const feedback = document.createElement('div');
  feedback.className = 'smartinput-feedback';
  feedback.innerHTML = `
    <div class="smartinput-feedback-content">
      <span class="smartinput-text">已插入: ${text}</span>
      <span class="smartinput-confidence">置信度: ${Math.round(confidence * 100)}%</span>
    </div>
  `;
  
  // 定位到输入框附近
  const rect = element.getBoundingClientRect();
  feedback.style.cssText = `
    position: fixed;
    top: ${rect.bottom + 5}px;
    left: ${rect.left}px;
    background: #4CAF50;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 10000;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    animation: smartinput-fade-in 0.3s ease-out;
  `;
  
  document.body.appendChild(feedback);
  
  // 3秒后移除
  setTimeout(() => {
    if (feedback.parentNode) {
      feedback.style.animation = 'smartinput-fade-out 0.3s ease-out';
      setTimeout(() => {
        if (feedback.parentNode) {
          feedback.parentNode.removeChild(feedback);
        }
      }, 300);
    }
  }, 3000);
}

// 显示无输入框警告
function showNoInputWarning(text) {
  const warning = document.createElement('div');
  warning.className = 'smartinput-warning';
  warning.innerHTML = `
    <div class="smartinput-warning-content">
      <strong>SmartInput</strong><br>
      未找到输入框，请点击一个文本输入框后重试<br>
      <small>文字: ${text}</small>
    </div>
  `;
  
  warning.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #FF9800;
    color: white;
    padding: 12px 16px;
    border-radius: 6px;
    font-size: 14px;
    z-index: 10000;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    max-width: 300px;
    animation: smartinput-slide-in 0.3s ease-out;
  `;
  
  document.body.appendChild(warning);
  
  // 5秒后移除
  setTimeout(() => {
    if (warning.parentNode) {
      warning.style.animation = 'smartinput-slide-out 0.3s ease-out';
      setTimeout(() => {
        if (warning.parentNode) {
          warning.parentNode.removeChild(warning);
        }
      }, 300);
    }
  }, 5000);
}

// 监听输入框点击，记录最近使用
document.addEventListener('click', (event) => {
  if (isInputElement(event.target)) {
    event.target.dataset.smartInputLastUsed = Date.now().toString();
  }
});

// 监听输入框焦点
document.addEventListener('focusin', (event) => {
  if (isInputElement(event.target)) {
    currentInputElement = event.target;
    event.target.dataset.smartInputLastUsed = Date.now().toString();
  }
});

console.log('SmartInput 内容脚本已加载');
