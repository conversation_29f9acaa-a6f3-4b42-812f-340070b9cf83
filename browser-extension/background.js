// SmartInput 浏览器扩展后台脚本

let socket = null;
let deviceId = null;
let isConnected = false;
let pairedDevices = new Set();

// 扩展安装时初始化
chrome.runtime.onInstalled.addListener(() => {
  console.log('SmartInput 扩展已安装');
  initializeExtension();
});

// 扩展启动时连接服务器
chrome.runtime.onStartup.addListener(() => {
  console.log('SmartInput 扩展启动');
  connectToServer();
});

// 初始化扩展
async function initializeExtension() {
  try {
    // 生成设备ID
    const result = await chrome.storage.local.get(['deviceId']);
    if (!result.deviceId) {
      deviceId = 'browser_' + Math.random().toString(36).substr(2, 9);
      await chrome.storage.local.set({ deviceId });
    } else {
      deviceId = result.deviceId;
    }
    
    // 连接到服务器
    connectToServer();
  } catch (error) {
    console.error('初始化失败:', error);
  }
}

// 连接到WebSocket服务器
function connectToServer() {
  try {
    // 注入Socket.io客户端
    importScripts('socket.io.js');
    
    socket = io('http://localhost:3001');
    
    socket.on('connect', () => {
      console.log('已连接到SmartInput服务器');
      isConnected = true;
      
      // 注册设备
      registerDevice();
      
      // 通知popup连接状态
      chrome.runtime.sendMessage({
        type: 'CONNECTION_STATUS',
        connected: true
      }).catch(() => {}); // 忽略没有接收者的错误
    });
    
    socket.on('disconnect', () => {
      console.log('与服务器断开连接');
      isConnected = false;
      
      chrome.runtime.sendMessage({
        type: 'CONNECTION_STATUS',
        connected: false
      }).catch(() => {});
    });
    
    socket.on('device:paired', (data) => {
      console.log('设备配对成功:', data);
      pairedDevices.add(data.mobileDeviceId);
      
      chrome.runtime.sendMessage({
        type: 'DEVICE_PAIRED',
        device: data
      }).catch(() => {});
    });
    
    socket.on('voice:text', (data) => {
      console.log('收到语音文字:', data);
      
      // 发送到当前活动标签页
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs[0]) {
          chrome.tabs.sendMessage(tabs[0].id, {
            type: 'INSERT_TEXT',
            text: data.text,
            confidence: data.confidence
          }).catch(() => {});
        }
      });
    });
    
    socket.on('error', (error) => {
      console.error('Socket连接错误:', error);
    });
    
  } catch (error) {
    console.error('连接服务器失败:', error);
  }
}

// 注册设备
function registerDevice() {
  if (!socket || !deviceId) return;
  
  const deviceInfo = {
    deviceId: deviceId,
    deviceName: 'Chrome浏览器',
    deviceType: 'browser',
    capabilities: {
      textInput: true,
      qrDisplay: true,
      notifications: true
    }
  };
  
  socket.emit('device:register', deviceInfo);
}

// 开始配对
function startPairing() {
  if (!socket) return;
  
  socket.emit('pairing:start');
  
  socket.once('pairing:code-generated', (data) => {
    console.log('配对码生成:', data);
    
    chrome.runtime.sendMessage({
      type: 'PAIRING_CODE',
      code: data.code,
      qrCode: data.qrCode
    }).catch(() => {});
  });
}

// 处理来自popup的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  switch (message.type) {
    case 'GET_STATUS':
      sendResponse({
        connected: isConnected,
        deviceId: deviceId,
        pairedDevices: Array.from(pairedDevices)
      });
      break;
      
    case 'START_PAIRING':
      startPairing();
      sendResponse({ success: true });
      break;
      
    case 'RECONNECT':
      connectToServer();
      sendResponse({ success: true });
      break;
      
    default:
      sendResponse({ error: 'Unknown message type' });
  }
  
  return true; // 保持消息通道开放
});

// 定期检查连接状态
setInterval(() => {
  if (!isConnected && socket) {
    console.log('尝试重新连接...');
    connectToServer();
  }
}, 30000); // 每30秒检查一次
