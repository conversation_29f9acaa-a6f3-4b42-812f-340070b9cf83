<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartInput 图标生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .icon-preview {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        .icon-item {
            text-align: center;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 8px;
            border: 2px solid #ddd;
        }
        .icon-item canvas {
            display: block;
            margin: 0 auto 10px;
            border: 1px solid #ccc;
        }
        .download-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .download-btn:hover {
            background: #45a049;
        }
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
        }
        .instructions h3 {
            margin-top: 0;
            color: #1976d2;
        }
        .instructions ol {
            margin: 15px 0;
        }
        .instructions li {
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>SmartInput 图标生成器</h1>
        <p style="text-align: center; color: #666;">
            为SmartInput浏览器扩展生成所需的PNG图标文件
        </p>
        
        <div class="icon-preview">
            <div class="icon-item">
                <canvas id="icon16" width="16" height="16"></canvas>
                <div>16x16</div>
                <button class="download-btn" onclick="downloadIcon('icon16', 'icon16.png')">下载</button>
            </div>
            <div class="icon-item">
                <canvas id="icon32" width="32" height="32"></canvas>
                <div>32x32</div>
                <button class="download-btn" onclick="downloadIcon('icon32', 'icon32.png')">下载</button>
            </div>
            <div class="icon-item">
                <canvas id="icon48" width="48" height="48"></canvas>
                <div>48x48</div>
                <button class="download-btn" onclick="downloadIcon('icon48', 'icon48.png')">下载</button>
            </div>
            <div class="icon-item">
                <canvas id="icon128" width="128" height="128"></canvas>
                <div>128x128</div>
                <button class="download-btn" onclick="downloadIcon('icon128', 'icon128.png')">下载</button>
            </div>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <button onclick="downloadAllIcons()" style="background: #2196F3; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-size: 16px;">
                下载所有图标
            </button>
        </div>
        
        <div class="instructions">
            <h3>使用说明</h3>
            <ol>
                <li>点击上方的"下载"按钮，分别下载每个尺寸的图标文件</li>
                <li>或者点击"下载所有图标"按钮一次性下载所有文件</li>
                <li>将下载的PNG文件保存到 <code>browser-extension/icons/</code> 目录</li>
                <li>确保文件名为: <code>icon16.png</code>, <code>icon32.png</code>, <code>icon48.png</code>, <code>icon128.png</code></li>
                <li>重新加载浏览器扩展以应用新图标</li>
            </ol>
        </div>
    </div>

    <script>
        // 绘制图标到canvas
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            const center = size / 2;
            const radius = size * 0.4;
            
            // 清空画布
            ctx.clearRect(0, 0, size, size);
            
            // 绘制渐变背景
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(center, center, radius, 0, 2 * Math.PI);
            ctx.fill();
            
            // 绘制麦克风
            ctx.fillStyle = 'white';
            ctx.strokeStyle = 'white';
            ctx.lineWidth = Math.max(1, size / 32);
            
            // 麦克风主体
            const micWidth = size * 0.15;
            const micHeight = size * 0.25;
            const micX = center - micWidth / 2;
            const micY = center - micHeight / 2 - size * 0.05;
            
            ctx.beginPath();
            ctx.roundRect(micX, micY, micWidth, micHeight, micWidth / 2);
            ctx.fill();
            
            // 麦克风支架
            ctx.beginPath();
            ctx.arc(center, micY + micHeight + size * 0.08, micWidth * 1.2, Math.PI, 0, false);
            ctx.stroke();
            
            // 麦克风底座
            ctx.beginPath();
            ctx.moveTo(center, micY + micHeight + size * 0.16);
            ctx.lineTo(center, micY + micHeight + size * 0.24);
            ctx.stroke();
            
            ctx.beginPath();
            ctx.moveTo(center - micWidth * 0.8, micY + micHeight + size * 0.24);
            ctx.lineTo(center + micWidth * 0.8, micY + micHeight + size * 0.24);
            ctx.stroke();
            
            // 声波效果（仅在较大尺寸显示）
            if (size >= 32) {
                ctx.globalAlpha = 0.7;
                ctx.beginPath();
                ctx.arc(center + size * 0.25, center - size * 0.05, size * 0.15, Math.PI * 0.7, Math.PI * 1.3, false);
                ctx.stroke();
                
                if (size >= 48) {
                    ctx.beginPath();
                    ctx.arc(center + size * 0.3, center - size * 0.05, size * 0.2, Math.PI * 0.65, Math.PI * 1.35, false);
                    ctx.stroke();
                }
                ctx.globalAlpha = 1;
            }
        }
        
        // 下载图标
        function downloadIcon(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        // 下载所有图标
        function downloadAllIcons() {
            const sizes = [16, 32, 48, 128];
            sizes.forEach((size, index) => {
                setTimeout(() => {
                    downloadIcon(`icon${size}`, `icon${size}.png`);
                }, index * 500); // 延迟下载避免浏览器阻止
            });
        }
        
        // 页面加载时绘制所有图标
        window.onload = function() {
            drawIcon(document.getElementById('icon16'), 16);
            drawIcon(document.getElementById('icon32'), 32);
            drawIcon(document.getElementById('icon48'), 48);
            drawIcon(document.getElementById('icon128'), 128);
        };
        
        // Polyfill for roundRect (older browsers)
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.beginPath();
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }
    </script>
</body>
</html>
