{"name": "smartinput-browser-extension", "version": "1.0.0", "description": "SmartInput 浏览器扩展 - 通过手机语音输入自动填充网页输入框", "main": "background.js", "scripts": {"build": "echo 'Browser extension build complete'", "dev": "echo 'Browser extension development mode'", "test": "echo 'No tests specified'", "lint": "echo 'No linting configured'", "package": "zip -r smartinput-extension.zip . -x '*.git*' 'node_modules/*' 'package*.json' '*.md' 'generate-icons.html'"}, "keywords": ["voice-input", "speech-recognition", "browser-extension", "chrome-extension", "smartinput", "accessibility"], "author": "SmartInput Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/smartinput/smartinput"}, "bugs": {"url": "https://github.com/smartinput/smartinput/issues"}, "homepage": "https://github.com/smartinput/smartinput#readme", "devDependencies": {}, "dependencies": {}, "engines": {"node": ">=14.0.0"}, "browserslist": ["Chrome >= 88"]}