<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SmartInput</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      width: 320px;
      min-height: 400px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f5f5f5;
    }
    
    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 16px;
      text-align: center;
    }
    
    .header h1 {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 4px;
    }
    
    .header p {
      font-size: 12px;
      opacity: 0.9;
    }
    
    .content {
      padding: 16px;
    }
    
    .status-card {
      background: white;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 16px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .status-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;
    }
    
    .status-item:last-child {
      margin-bottom: 0;
    }
    
    .status-label {
      font-size: 14px;
      color: #666;
    }
    
    .status-value {
      font-size: 14px;
      font-weight: 500;
    }
    
    .status-connected {
      color: #4CAF50;
    }
    
    .status-disconnected {
      color: #f44336;
    }
    
    .btn {
      width: 100%;
      padding: 12px;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
      margin-bottom: 8px;
    }
    
    .btn:last-child {
      margin-bottom: 0;
    }
    
    .btn-primary {
      background: #2196F3;
      color: white;
    }
    
    .btn-primary:hover {
      background: #1976D2;
    }
    
    .btn-secondary {
      background: #f0f0f0;
      color: #333;
    }
    
    .btn-secondary:hover {
      background: #e0e0e0;
    }
    
    .btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    
    .qr-container {
      text-align: center;
      padding: 16px;
      background: white;
      border-radius: 8px;
      margin-bottom: 16px;
      display: none;
    }
    
    .qr-code {
      max-width: 200px;
      height: auto;
      margin: 16px auto;
    }
    
    .pairing-code {
      font-family: 'Courier New', monospace;
      font-size: 18px;
      font-weight: bold;
      color: #2196F3;
      background: #f0f8ff;
      padding: 8px 16px;
      border-radius: 4px;
      margin: 8px 0;
      letter-spacing: 2px;
    }
    
    .device-list {
      background: white;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 16px;
    }
    
    .device-item {
      display: flex;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #eee;
    }
    
    .device-item:last-child {
      border-bottom: none;
    }
    
    .device-icon {
      width: 32px;
      height: 32px;
      background: #e3f2fd;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      font-size: 16px;
    }
    
    .device-info {
      flex: 1;
    }
    
    .device-name {
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }
    
    .device-status {
      font-size: 12px;
      color: #666;
    }
    
    .loading {
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #2196F3;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .hidden {
      display: none !important;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>SmartInput</h1>
    <p>智能语音输入助手</p>
  </div>
  
  <div class="content">
    <!-- 连接状态 -->
    <div class="status-card">
      <div class="status-item">
        <span class="status-label">服务器连接</span>
        <span id="connectionStatus" class="status-value status-disconnected">未连接</span>
      </div>
      <div class="status-item">
        <span class="status-label">设备ID</span>
        <span id="deviceId" class="status-value">-</span>
      </div>
      <div class="status-item">
        <span class="status-label">配对设备</span>
        <span id="pairedCount" class="status-value">0</span>
      </div>
    </div>
    
    <!-- QR码显示 -->
    <div id="qrContainer" class="qr-container">
      <h3>扫描二维码配对</h3>
      <div id="qrCode"></div>
      <div class="pairing-code" id="pairingCode">-</div>
      <p style="font-size: 12px; color: #666; margin-top: 8px;">
        使用手机SmartInput应用扫描上方二维码
      </p>
    </div>
    
    <!-- 配对设备列表 -->
    <div id="deviceList" class="device-list hidden">
      <h3 style="margin-bottom: 12px; font-size: 16px;">已配对设备</h3>
      <div id="devices"></div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="actions">
      <button id="pairBtn" class="btn btn-primary" disabled>
        <span id="pairBtnText">开始配对</span>
        <span id="pairBtnLoading" class="loading hidden"></span>
      </button>
      <button id="reconnectBtn" class="btn btn-secondary">重新连接</button>
    </div>
    
    <!-- 使用说明 -->
    <div style="margin-top: 16px; padding: 12px; background: #fff3cd; border-radius: 6px; font-size: 12px; color: #856404;">
      <strong>使用说明：</strong><br>
      1. 确保SmartInput服务器正在运行<br>
      2. 点击"开始配对"生成二维码<br>
      3. 用手机应用扫描二维码<br>
      4. 在网页输入框中点击，然后使用手机语音输入
    </div>
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
