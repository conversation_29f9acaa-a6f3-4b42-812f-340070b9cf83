<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="64" cy="64" r="60" fill="url(#grad1)" stroke="#fff" stroke-width="4"/>
  
  <!-- 麦克风图标 -->
  <g transform="translate(64, 64)">
    <!-- 麦克风主体 -->
    <rect x="-8" y="-20" width="16" height="24" rx="8" fill="#fff"/>
    
    <!-- 麦克风支架 -->
    <path d="M -20 -5 Q -20 10 0 10 Q 20 10 20 -5" stroke="#fff" stroke-width="3" fill="none"/>
    
    <!-- 麦克风底座 -->
    <line x1="0" y1="10" x2="0" y2="20" stroke="#fff" stroke-width="3"/>
    <line x1="-10" y1="20" x2="10" y2="20" stroke="#fff" stroke-width="3"/>
    
    <!-- 声波效果 -->
    <g opacity="0.7">
      <path d="M 25 -15 Q 35 0 25 15" stroke="#fff" stroke-width="2" fill="none"/>
      <path d="M 30 -20 Q 42 0 30 20" stroke="#fff" stroke-width="2" fill="none"/>
    </g>
  </g>
  
  <!-- 文字 -->
  <text x="64" y="110" text-anchor="middle" fill="#fff" font-family="Arial, sans-serif" font-size="12" font-weight="bold">SmartInput</text>
</svg>
