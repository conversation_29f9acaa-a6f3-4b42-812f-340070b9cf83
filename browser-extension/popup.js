// SmartInput 浏览器扩展弹窗脚本

let isConnected = false;
let isPairing = false;
let pairedDevices = [];

// DOM元素
const connectionStatus = document.getElementById('connectionStatus');
const deviceId = document.getElementById('deviceId');
const pairedCount = document.getElementById('pairedCount');
const qrContainer = document.getElementById('qrContainer');
const qrCode = document.getElementById('qrCode');
const pairingCode = document.getElementById('pairingCode');
const deviceList = document.getElementById('deviceList');
const devices = document.getElementById('devices');
const pairBtn = document.getElementById('pairBtn');
const pairBtnText = document.getElementById('pairBtnText');
const pairBtnLoading = document.getElementById('pairBtnLoading');
const reconnectBtn = document.getElementById('reconnectBtn');

// 初始化
document.addEventListener('DOMContentLoaded', () => {
  loadStatus();
  setupEventListeners();
});

// 设置事件监听器
function setupEventListeners() {
  pairBtn.addEventListener('click', handlePairClick);
  reconnectBtn.addEventListener('click', handleReconnectClick);
  
  // 监听来自background的消息
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    switch (message.type) {
      case 'CONNECTION_STATUS':
        updateConnectionStatus(message.connected);
        break;
        
      case 'DEVICE_PAIRED':
        handleDevicePaired(message.device);
        break;
        
      case 'PAIRING_CODE':
        showPairingCode(message.code, message.qrCode);
        break;
    }
  });
}

// 加载状态
async function loadStatus() {
  try {
    const response = await chrome.runtime.sendMessage({ type: 'GET_STATUS' });
    
    if (response) {
      updateConnectionStatus(response.connected);
      updateDeviceId(response.deviceId);
      updatePairedDevices(response.pairedDevices || []);
    }
  } catch (error) {
    console.error('加载状态失败:', error);
  }
}

// 更新连接状态
function updateConnectionStatus(connected) {
  isConnected = connected;
  
  if (connected) {
    connectionStatus.textContent = '已连接';
    connectionStatus.className = 'status-value status-connected';
    pairBtn.disabled = false;
  } else {
    connectionStatus.textContent = '未连接';
    connectionStatus.className = 'status-value status-disconnected';
    pairBtn.disabled = true;
    hidePairingCode();
  }
}

// 更新设备ID
function updateDeviceId(id) {
  if (id) {
    deviceId.textContent = id.substring(0, 12) + '...';
    deviceId.title = id;
  } else {
    deviceId.textContent = '-';
  }
}

// 更新配对设备列表
function updatePairedDevices(devices) {
  pairedDevices = devices;
  pairedCount.textContent = devices.length;
  
  if (devices.length > 0) {
    showDeviceList(devices);
  } else {
    hideDeviceList();
  }
}

// 显示设备列表
function showDeviceList(devices) {
  const devicesContainer = document.getElementById('devices');
  devicesContainer.innerHTML = '';
  
  devices.forEach(deviceId => {
    const deviceItem = document.createElement('div');
    deviceItem.className = 'device-item';
    deviceItem.innerHTML = `
      <div class="device-icon">📱</div>
      <div class="device-info">
        <div class="device-name">移动设备</div>
        <div class="device-status">${deviceId.substring(0, 16)}...</div>
      </div>
    `;
    devicesContainer.appendChild(deviceItem);
  });
  
  deviceList.classList.remove('hidden');
}

// 隐藏设备列表
function hideDeviceList() {
  deviceList.classList.add('hidden');
}

// 处理配对按钮点击
async function handlePairClick() {
  if (isPairing) {
    // 停止配对
    stopPairing();
  } else {
    // 开始配对
    startPairing();
  }
}

// 开始配对
async function startPairing() {
  try {
    isPairing = true;
    updatePairButton();
    
    const response = await chrome.runtime.sendMessage({ type: 'START_PAIRING' });
    
    if (!response.success) {
      throw new Error('启动配对失败');
    }
    
  } catch (error) {
    console.error('配对失败:', error);
    stopPairing();
    alert('配对失败: ' + error.message);
  }
}

// 停止配对
function stopPairing() {
  isPairing = false;
  updatePairButton();
  hidePairingCode();
}

// 更新配对按钮状态
function updatePairButton() {
  if (isPairing) {
    pairBtnText.textContent = '停止配对';
    pairBtnLoading.classList.remove('hidden');
    pairBtn.className = 'btn btn-secondary';
  } else {
    pairBtnText.textContent = '开始配对';
    pairBtnLoading.classList.add('hidden');
    pairBtn.className = 'btn btn-primary';
  }
}

// 显示配对码
function showPairingCode(code, qrCodeData) {
  pairingCode.textContent = code;
  
  // 显示QR码
  if (qrCodeData) {
    qrCode.innerHTML = `<img src="${qrCodeData}" alt="配对二维码" class="qr-code">`;
  }
  
  qrContainer.style.display = 'block';
  
  // 30秒后自动隐藏
  setTimeout(() => {
    if (isPairing) {
      stopPairing();
    }
  }, 30000);
}

// 隐藏配对码
function hidePairingCode() {
  qrContainer.style.display = 'none';
  qrCode.innerHTML = '';
  pairingCode.textContent = '-';
}

// 处理设备配对成功
function handleDevicePaired(device) {
  console.log('设备配对成功:', device);
  
  // 停止配对
  stopPairing();
  
  // 更新设备列表
  if (!pairedDevices.includes(device.mobileDeviceId)) {
    pairedDevices.push(device.mobileDeviceId);
    updatePairedDevices(pairedDevices);
  }
  
  // 显示成功提示
  showNotification('设备配对成功！', 'success');
}

// 处理重连按钮点击
async function handleReconnectClick() {
  try {
    reconnectBtn.disabled = true;
    reconnectBtn.textContent = '连接中...';
    
    await chrome.runtime.sendMessage({ type: 'RECONNECT' });
    
    // 等待一下再重新加载状态
    setTimeout(() => {
      loadStatus();
      reconnectBtn.disabled = false;
      reconnectBtn.textContent = '重新连接';
    }, 2000);
    
  } catch (error) {
    console.error('重连失败:', error);
    reconnectBtn.disabled = false;
    reconnectBtn.textContent = '重新连接';
  }
}

// 显示通知
function showNotification(message, type = 'info') {
  // 创建通知元素
  const notification = document.createElement('div');
  notification.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    background: ${type === 'success' ? '#4CAF50' : '#2196F3'};
    color: white;
    padding: 12px 16px;
    border-radius: 4px;
    font-size: 14px;
    z-index: 10000;
    animation: slideIn 0.3s ease-out;
  `;
  notification.textContent = message;
  
  document.body.appendChild(notification);
  
  // 3秒后移除
  setTimeout(() => {
    if (notification.parentNode) {
      notification.style.animation = 'slideOut 0.3s ease-out';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }
  }, 3000);
}

// 添加样式
const style = document.createElement('style');
style.textContent = `
  @keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
  }
  
  @keyframes slideOut {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
  }
`;
document.head.appendChild(style);
