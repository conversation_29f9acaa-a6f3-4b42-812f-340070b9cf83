// 简单的连接测试脚本
const io = require('socket.io-client');

console.log('正在连接到 SmartInput 服务器...');

const socket = io('http://localhost:3001');

socket.on('connect', () => {
  console.log('✅ 成功连接到服务器');
  console.log('Socket ID:', socket.id);
  
  // 测试设备注册
  const deviceInfo = {
    deviceId: 'test-device-' + Date.now(),
    deviceName: '测试设备',
    deviceType: 'mobile',
    capabilities: {
      speechRecognition: true,
      camera: true,
      microphone: true,
      vibration: true
    }
  };
  
  console.log('正在注册设备...');
  socket.emit('device:register', deviceInfo);
});

socket.on('device:registered', (data) => {
  console.log('✅ 设备注册成功:', data);
  
  // 测试配对码生成
  console.log('正在生成配对码...');
  socket.emit('pairing:start');
});

socket.on('pairing:code-generated', (data) => {
  console.log('✅ 配对码生成成功:', data);
  
  // 模拟语音输入
  setTimeout(() => {
    console.log('正在发送测试语音文本...');
    socket.emit('voice:text', {
      text: '这是一个测试语音输入',
      confidence: 0.95,
      timestamp: Date.now()
    });
  }, 2000);
});

socket.on('voice:text-sent', (data) => {
  console.log('✅ 语音文本发送成功:', data);
  
  // 测试完成，断开连接
  setTimeout(() => {
    console.log('测试完成，断开连接');
    socket.disconnect();
    process.exit(0);
  }, 1000);
});

socket.on('error', (error) => {
  console.error('❌ 连接错误:', error);
});

socket.on('disconnect', (reason) => {
  console.log('🔌 连接断开:', reason);
});

// 超时处理
setTimeout(() => {
  console.log('⏰ 测试超时');
  socket.disconnect();
  process.exit(1);
}, 10000);
