{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@components/*": ["./src/components/*"], "@pages/*": ["./src/pages/*"], "@services/*": ["./src/services/*"], "@stores/*": ["./src/stores/*"], "@utils/*": ["./src/utils/*"], "@hooks/*": ["./src/hooks/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"], "references": [{"path": "./tsconfig.node.json"}]}