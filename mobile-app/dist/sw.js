if(!self.define){let e,s={};const n=(n,i)=>(n=new URL(n+".js",i).href,s[n]||new Promise(s=>{if("document"in self){const e=document.createElement("script");e.src=n,e.onload=s,document.head.appendChild(e)}else e=n,importScripts(n),s()}).then(()=>{let e=s[n];if(!e)throw new Error(`Module ${n} didn’t register its module`);return e}));self.define=(i,r)=>{const t=e||("document"in self?document.currentScript.src:"")||location.href;if(s[t])return;let l={};const o=e=>n(e,t),c={module:{uri:t},exports:l,require:o};s[t]=Promise.all(i.map(e=>c[e]||o(e))).then(e=>(r(...e),l))}}define(["./workbox-1ea6f077"],function(e){"use strict";self.skipWaiting(),e.clients<PERSON>laim(),e.precacheAndRoute([{url:"assets/index-0672b2ea.css",revision:null},{url:"assets/index-3ec62145.js",revision:null},{url:"assets/qr-3e38ac8d.js",revision:null},{url:"assets/qr-scanner-worker.min-5f44a019.js",revision:null},{url:"assets/socket-e2a77347.js",revision:null},{url:"assets/vendor-ccbdd736.js",revision:null},{url:"index.html",revision:"de89bc6f95698c09975962a4334fb2f8"},{url:"registerSW.js",revision:"1872c500de691dce40960bb85481de07"},{url:"manifest.webmanifest",revision:"84c6187340f711187c821448f9ef5144"}],{}),e.cleanupOutdatedCaches(),e.registerRoute(new e.NavigationRoute(e.createHandlerBoundToURL("index.html"))),e.registerRoute(/^https:\/\/api\./,new e.NetworkFirst({cacheName:"api-cache",plugins:[new e.ExpirationPlugin({maxEntries:100,maxAgeSeconds:86400})]}),"GET")});
