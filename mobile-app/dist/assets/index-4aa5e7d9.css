@tailwind base;@tailwind components;@tailwind utilities;.safe-area-top{padding-top:env(safe-area-inset-top)}.safe-area-bottom{padding-bottom:env(safe-area-inset-bottom)}.safe-area-left{padding-left:env(safe-area-inset-left)}.safe-area-right{padding-right:env(safe-area-inset-right)}@layer components{.card{@apply bg-white rounded-xl shadow-sm border border-gray-200 p-4;}.btn{@apply inline-flex items-center justify-center px-4 py-2 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;}.btn-primary{@apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;}.btn-secondary{@apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;}.btn-danger{@apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;}.btn-sm{@apply px-3 py-1.5 text-sm;}.btn-lg{@apply px-6 py-3 text-lg;}.input{@apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;}.switch{@apply relative inline-block w-12 h-6;}.switch input{@apply opacity-0 w-0 h-0;}.slider{@apply absolute cursor-pointer top-0 left-0 right-0 bottom-0 bg-gray-300 rounded-full transition-all duration-300;}.slider:before{@apply absolute content-[""] h-5 w-5 left-0.5 bottom-0.5 bg-white rounded-full transition-all duration-300;}input:checked+.slider{@apply bg-blue-600;}input:checked+.slider:before{@apply transform translate-x-6;}.recording-button{@apply w-20 h-20 rounded-full flex items-center justify-center transition-all duration-200 shadow-lg;}.recording-button.idle{@apply bg-blue-600 text-white hover:bg-blue-700 active:scale-95;}.recording-button.recording{@apply bg-red-600 text-white animate-pulse;}.recording-button.processing{@apply bg-yellow-600 text-white;}.recording-button.error{@apply bg-gray-400 text-white cursor-not-allowed;}.recording-button:disabled{@apply opacity-50 cursor-not-allowed;}}@keyframes fade-in{0%{opacity:0;transform:translateY(-10px)}to{opacity:1;transform:translateY(0)}}@keyframes fade-out{0%{opacity:1;transform:translateY(0)}to{opacity:0;transform:translateY(-10px)}}.animate-fade-in{animation:fade-in .3s ease-out}.animate-fade-out{animation:fade-out .3s ease-out}.touch-manipulation{touch-action:manipulation}.no-select{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}::-webkit-scrollbar{width:4px}::-webkit-scrollbar-track{background:#f1f1f1}::-webkit-scrollbar-thumb{background:#c1c1c1;border-radius:2px}::-webkit-scrollbar-thumb:hover{background:#a1a1a1}@media (display-mode: standalone){body{-webkit-user-select:none;-webkit-touch-callout:none}}@layer base{html{font-family:Inter,system-ui,sans-serif;-webkit-text-size-adjust:100%;-webkit-tap-highlight-color:transparent}body{margin:0;padding:0;min-height:100vh;background-color:#f8fafc;color:#1e293b;font-size:16px;line-height:1.5;overflow-x:hidden}*{box-sizing:border-box}#root{min-height:100vh;display:flex;flex-direction:column}}@layer components{.btn{@apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;}.btn-primary{@apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;}.btn-secondary{@apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;}.btn-danger{@apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;}.btn-success{@apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;}.btn-lg{@apply px-6 py-3 text-base;}.btn-sm{@apply px-3 py-1.5 text-xs;}.card{@apply bg-white rounded-xl shadow-sm border border-gray-200 p-6;}.input{@apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;}.status-dot{@apply w-3 h-3 rounded-full;}.status-connected{@apply bg-green-500;}.status-connecting{@apply bg-yellow-500 animate-pulse;}.status-disconnected{@apply bg-red-500;}.recording-button{@apply w-20 h-20 rounded-full flex items-center justify-center text-white font-bold text-lg transition-all duration-200 active:scale-95;}.recording-button.idle{@apply bg-blue-600 hover:bg-blue-700 shadow-lg;}.recording-button.recording{@apply bg-red-600 animate-pulse shadow-xl scale-110;}.recording-button.processing{@apply bg-yellow-600 animate-spin;}.wave-animation{@apply relative overflow-hidden;}.wave-animation:before{content:"";@apply absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent;animation:wave 2s infinite;transform:translate(-100%)}@keyframes wave{0%{transform:translate(-100%)}to{transform:translate(100%)}}.fade-in{animation:fadeIn .3s ease-in-out}@keyframes fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}.slide-up{animation:slideUp .3s ease-out}@keyframes slideUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}.bounce-in{animation:bounceIn .5s ease-out}@keyframes bounceIn{0%{opacity:0;transform:scale(.3)}50%{opacity:1;transform:scale(1.05)}70%{transform:scale(.9)}to{opacity:1;transform:scale(1)}}}@layer utilities{.safe-area-top{padding-top:env(safe-area-inset-top)}.safe-area-bottom{padding-bottom:env(safe-area-inset-bottom)}.safe-area-left{padding-left:env(safe-area-inset-left)}.safe-area-right{padding-right:env(safe-area-inset-right)}.touch-manipulation{touch-action:manipulation}.no-scrollbar{-ms-overflow-style:none;scrollbar-width:none}.no-scrollbar::-webkit-scrollbar{display:none}}@media (max-width: 768px){.btn{@apply min-h-[44px] min-w-[44px];}.recording-button{@apply w-24 h-24;}}@media (prefers-color-scheme: dark){body{@apply bg-gray-900 text-gray-100;}.card{@apply bg-gray-800 border-gray-700;}.input{@apply bg-gray-800 border-gray-600 text-gray-100;}}
