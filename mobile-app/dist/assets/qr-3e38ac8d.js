var m=(S,e,t)=>new Promise((i,a)=>{var s=c=>{try{d(t.next(c))}catch(h){a(h)}},r=c=>{try{d(t.throw(c))}catch(h){a(h)}},d=c=>c.done?i(c.value):Promise.resolve(c.value).then(s,r);d((t=t.apply(S,e)).next())});const O="modulepreload",C=function(S){return"/"+S},$={},R=function(e,t,i){if(!t||t.length===0)return e();const a=document.getElementsByTagName("link");return Promise.all(t.map(s=>{if(s=C(s),s in $)return;$[s]=!0;const r=s.endsWith(".css"),d=r?'[rel="stylesheet"]':"";if(!!i)for(let l=a.length-1;l>=0;l--){const v=a[l];if(v.href===s&&(!r||v.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${s}"]${d}`))return;const h=document.createElement("link");if(h.rel=r?"stylesheet":O,r||(h.as="script",h.crossOrigin=""),h.href=s,document.head.appendChild(h),r)return new Promise((l,v)=>{h.addEventListener("load",l),h.addEventListener("error",()=>v(new Error(`Unable to preload CSS for ${s}`)))})})).then(()=>e()).catch(s=>{const r=new Event("vite:preloadError",{cancelable:!0});if(r.payload=s,window.dispatchEvent(r),!r.defaultPrevented)throw s})};class n{constructor(e,t,i,a,s){this._legacyCanvasSize=n.DEFAULT_CANVAS_SIZE,this._preferredCamera="environment",this._maxScansPerSecond=25,this._lastScanTimestamp=-1,this._destroyed=this._flashOn=this._paused=this._active=!1,this.$video=e,this.$canvas=document.createElement("canvas"),i&&typeof i=="object"?this._onDecode=t:(console.warn(i||a||s?"You're using a deprecated version of the QrScanner constructor which will be removed in the future":"Note that the type of the scan result passed to onDecode will change in the future. To already switch to the new api today, you can pass returnDetailedScanResult: true."),this._legacyOnDecode=t),t=typeof i=="object"?i:{},this._onDecodeError=t.onDecodeError||(typeof i=="function"?i:this._onDecodeError),this._calculateScanRegion=t.calculateScanRegion||(typeof a=="function"?a:this._calculateScanRegion),this._preferredCamera=t.preferredCamera||s||this._preferredCamera,this._legacyCanvasSize=typeof i=="number"?i:typeof a=="number"?a:this._legacyCanvasSize,this._maxScansPerSecond=t.maxScansPerSecond||this._maxScansPerSecond,this._onPlay=this._onPlay.bind(this),this._onLoadedMetaData=this._onLoadedMetaData.bind(this),this._onVisibilityChange=this._onVisibilityChange.bind(this),this._updateOverlay=this._updateOverlay.bind(this),e.disablePictureInPicture=!0,e.playsInline=!0,e.muted=!0;let r=!1;if(e.hidden&&(e.hidden=!1,r=!0),document.body.contains(e)||(document.body.appendChild(e),r=!0),i=e.parentElement,t.highlightScanRegion||t.highlightCodeOutline){if(a=!!t.overlay,this.$overlay=t.overlay||document.createElement("div"),s=this.$overlay.style,s.position="absolute",s.display="none",s.pointerEvents="none",this.$overlay.classList.add("scan-region-highlight"),!a&&t.highlightScanRegion){this.$overlay.innerHTML='<svg class="scan-region-highlight-svg" viewBox="0 0 238 238" preserveAspectRatio="none" style="position:absolute;width:100%;height:100%;left:0;top:0;fill:none;stroke:#e9b213;stroke-width:4;stroke-linecap:round;stroke-linejoin:round"><path d="M31 2H10a8 8 0 0 0-8 8v21M207 2h21a8 8 0 0 1 8 8v21m0 176v21a8 8 0 0 1-8 8h-21m-176 0H10a8 8 0 0 1-8-8v-21"/></svg>';try{this.$overlay.firstElementChild.animate({transform:["scale(.98)","scale(1.01)"]},{duration:400,iterations:1/0,direction:"alternate",easing:"ease-in-out"})}catch(d){}i.insertBefore(this.$overlay,this.$video.nextSibling)}t.highlightCodeOutline&&(this.$overlay.insertAdjacentHTML("beforeend",'<svg class="code-outline-highlight" preserveAspectRatio="none" style="display:none;width:100%;height:100%;fill:none;stroke:#e9b213;stroke-width:5;stroke-dasharray:25;stroke-linecap:round;stroke-linejoin:round"><polygon/></svg>'),this.$codeOutlineHighlight=this.$overlay.lastElementChild)}this._scanRegion=this._calculateScanRegion(e),requestAnimationFrame(()=>{let d=window.getComputedStyle(e);d.display==="none"&&(e.style.setProperty("display","block","important"),r=!0),d.visibility!=="visible"&&(e.style.setProperty("visibility","visible","important"),r=!0),r&&(console.warn("QrScanner has overwritten the video hiding style to avoid Safari stopping the playback."),e.style.opacity="0",e.style.width="0",e.style.height="0",this.$overlay&&this.$overlay.parentElement&&this.$overlay.parentElement.removeChild(this.$overlay),delete this.$overlay,delete this.$codeOutlineHighlight),this.$overlay&&this._updateOverlay()}),e.addEventListener("play",this._onPlay),e.addEventListener("loadedmetadata",this._onLoadedMetaData),document.addEventListener("visibilitychange",this._onVisibilityChange),window.addEventListener("resize",this._updateOverlay),this._qrEnginePromise=n.createQrEngine()}static set WORKER_PATH(e){console.warn("Setting QrScanner.WORKER_PATH is not required and not supported anymore. Have a look at the README for new setup instructions.")}static hasCamera(){return m(this,null,function*(){try{return!!(yield n.listCameras(!1)).length}catch(e){return!1}})}static listCameras(e=!1){return m(this,null,function*(){if(!navigator.mediaDevices)return[];let t=()=>m(this,null,function*(){return(yield navigator.mediaDevices.enumerateDevices()).filter(a=>a.kind==="videoinput")}),i;try{e&&(yield t()).every(a=>!a.label)&&(i=yield navigator.mediaDevices.getUserMedia({audio:!1,video:!0}))}catch(a){}try{return(yield t()).map((a,s)=>({id:a.deviceId,label:a.label||(s===0?"Default Camera":`Camera ${s+1}`)}))}finally{i&&(console.warn("Call listCameras after successfully starting a QR scanner to avoid creating a temporary video stream"),n._stopVideoStream(i))}})}hasFlash(){return m(this,null,function*(){let e;try{if(this.$video.srcObject){if(!(this.$video.srcObject instanceof MediaStream))return!1;e=this.$video.srcObject}else e=(yield this._getCameraStream()).stream;return"torch"in e.getVideoTracks()[0].getSettings()}catch(t){return!1}finally{e&&e!==this.$video.srcObject&&(console.warn("Call hasFlash after successfully starting the scanner to avoid creating a temporary video stream"),n._stopVideoStream(e))}})}isFlashOn(){return this._flashOn}toggleFlash(){return m(this,null,function*(){this._flashOn?yield this.turnFlashOff():yield this.turnFlashOn()})}turnFlashOn(){return m(this,null,function*(){if(!this._flashOn&&!this._destroyed&&(this._flashOn=!0,this._active&&!this._paused))try{if(!(yield this.hasFlash()))throw"No flash available";yield this.$video.srcObject.getVideoTracks()[0].applyConstraints({advanced:[{torch:!0}]})}catch(e){throw this._flashOn=!1,e}})}turnFlashOff(){return m(this,null,function*(){this._flashOn&&(this._flashOn=!1,yield this._restartVideoStream())})}destroy(){this.$video.removeEventListener("loadedmetadata",this._onLoadedMetaData),this.$video.removeEventListener("play",this._onPlay),document.removeEventListener("visibilitychange",this._onVisibilityChange),window.removeEventListener("resize",this._updateOverlay),this._destroyed=!0,this._flashOn=!1,this.stop(),n._postWorkerMessage(this._qrEnginePromise,"close")}start(){return m(this,null,function*(){if(this._destroyed)throw Error("The QR scanner can not be started as it had been destroyed.");if((!this._active||this._paused)&&(window.location.protocol!=="https:"&&console.warn("The camera stream is only accessible if the page is transferred via https."),this._active=!0,!document.hidden))if(this._paused=!1,this.$video.srcObject)yield this.$video.play();else try{let{stream:e,facingMode:t}=yield this._getCameraStream();!this._active||this._paused?n._stopVideoStream(e):(this._setVideoMirror(t),this.$video.srcObject=e,yield this.$video.play(),this._flashOn&&(this._flashOn=!1,this.turnFlashOn().catch(()=>{})))}catch(e){if(!this._paused)throw this._active=!1,e}})}stop(){this.pause(),this._active=!1}pause(e=!1){return m(this,null,function*(){if(this._paused=!0,!this._active)return!0;this.$video.pause(),this.$overlay&&(this.$overlay.style.display="none");let t=()=>{this.$video.srcObject instanceof MediaStream&&(n._stopVideoStream(this.$video.srcObject),this.$video.srcObject=null)};return e?(t(),!0):(yield new Promise(i=>setTimeout(i,300)),this._paused?(t(),!0):!1)})}setCamera(e){return m(this,null,function*(){e!==this._preferredCamera&&(this._preferredCamera=e,yield this._restartVideoStream())})}static scanImage(e,t,i,a,s=!1,r=!1){return m(this,null,function*(){let d,c=!1;t&&("scanRegion"in t||"qrEngine"in t||"canvas"in t||"disallowCanvasResizing"in t||"alsoTryWithoutScanRegion"in t||"returnDetailedScanResult"in t)?(d=t.scanRegion,i=t.qrEngine,a=t.canvas,s=t.disallowCanvasResizing||!1,r=t.alsoTryWithoutScanRegion||!1,c=!0):console.warn(t||i||a||s||r?"You're using a deprecated api for scanImage which will be removed in the future.":"Note that the return type of scanImage will change in the future. To already switch to the new api today, you can pass returnDetailedScanResult: true."),t=!!i;try{let h,l;[i,h]=yield Promise.all([i||n.createQrEngine(),n._loadImage(e)]),[a,l]=n._drawToCanvas(h,d,a,s);let v;if(i instanceof Worker){let o=i;t||n._postWorkerMessageSync(o,"inversionMode","both"),v=yield new Promise((g,p)=>{let _,f,u,E=-1;f=y=>{y.data.id===E&&(o.removeEventListener("message",f),o.removeEventListener("error",u),clearTimeout(_),y.data.data!==null?g({data:y.data.data,cornerPoints:n._convertPoints(y.data.cornerPoints,d)}):p(n.NO_QR_CODE_FOUND))},u=y=>{o.removeEventListener("message",f),o.removeEventListener("error",u),clearTimeout(_),p("Scanner error: "+(y?y.message||y:"Unknown Error"))},o.addEventListener("message",f),o.addEventListener("error",u),_=setTimeout(()=>u("timeout"),1e4);let w=l.getImageData(0,0,a.width,a.height);E=n._postWorkerMessageSync(o,"decode",w,[w.data.buffer])})}else v=yield Promise.race([new Promise((o,g)=>window.setTimeout(()=>g("Scanner error: timeout"),1e4)),(()=>m(this,null,function*(){try{var[o]=yield i.detect(a);if(!o)throw n.NO_QR_CODE_FOUND;return{data:o.rawValue,cornerPoints:n._convertPoints(o.cornerPoints,d)}}catch(g){if(o=g.message||g,/not implemented|service unavailable/.test(o))return n._disableBarcodeDetector=!0,n.scanImage(e,{scanRegion:d,canvas:a,disallowCanvasResizing:s,alsoTryWithoutScanRegion:r});throw`Scanner error: ${o}`}}))()]);return c?v:v.data}catch(h){if(!d||!r)throw h;let l=yield n.scanImage(e,{qrEngine:i,canvas:a,disallowCanvasResizing:s});return c?l:l.data}finally{t||n._postWorkerMessage(i,"close")}})}setGrayscaleWeights(e,t,i,a=!0){n._postWorkerMessage(this._qrEnginePromise,"grayscaleWeights",{red:e,green:t,blue:i,useIntegerApproximation:a})}setInversionMode(e){n._postWorkerMessage(this._qrEnginePromise,"inversionMode",e)}static createQrEngine(e){return m(this,null,function*(){if(e&&console.warn("Specifying a worker path is not required and not supported anymore."),e=()=>R(()=>import("./qr-scanner-worker.min-5f44a019.js"),[]).then(i=>i.createWorker()),!(!n._disableBarcodeDetector&&"BarcodeDetector"in window&&BarcodeDetector.getSupportedFormats&&(yield BarcodeDetector.getSupportedFormats()).includes("qr_code")))return e();let t=navigator.userAgentData;return t&&t.brands.some(({brand:i})=>/Chromium/i.test(i))&&/mac ?OS/i.test(t.platform)&&(yield t.getHighEntropyValues(["architecture","platformVersion"]).then(({architecture:i,platformVersion:a})=>/arm/i.test(i||"arm")&&13<=parseInt(a||"13")).catch(()=>!0))?e():new BarcodeDetector({formats:["qr_code"]})})}_onPlay(){this._scanRegion=this._calculateScanRegion(this.$video),this._updateOverlay(),this.$overlay&&(this.$overlay.style.display=""),this._scanFrame()}_onLoadedMetaData(){this._scanRegion=this._calculateScanRegion(this.$video),this._updateOverlay()}_onVisibilityChange(){document.hidden?this.pause():this._active&&this.start()}_calculateScanRegion(e){let t=Math.round(.6666666666666666*Math.min(e.videoWidth,e.videoHeight));return{x:Math.round((e.videoWidth-t)/2),y:Math.round((e.videoHeight-t)/2),width:t,height:t,downScaledWidth:this._legacyCanvasSize,downScaledHeight:this._legacyCanvasSize}}_updateOverlay(){requestAnimationFrame(()=>{if(this.$overlay){var e=this.$video,t=e.videoWidth,i=e.videoHeight,a=e.offsetWidth,s=e.offsetHeight,r=e.offsetLeft,d=e.offsetTop,c=window.getComputedStyle(e),h=c.objectFit,l=t/i,v=a/s;switch(h){case"none":var o=t,g=i;break;case"fill":o=a,g=s;break;default:(h==="cover"?l>v:l<v)?(g=s,o=g*l):(o=a,g=o/l),h==="scale-down"&&(o=Math.min(o,t),g=Math.min(g,i))}var[p,_]=c.objectPosition.split(" ").map((u,E)=>{const w=parseFloat(u);return u.endsWith("%")?(E?s-g:a-o)*w/100:w});c=this._scanRegion.width||t,v=this._scanRegion.height||i,h=this._scanRegion.x||0;var f=this._scanRegion.y||0;l=this.$overlay.style,l.width=`${c/t*o}px`,l.height=`${v/i*g}px`,l.top=`${d+_+f/i*g}px`,i=/scaleX\(-1\)/.test(e.style.transform),l.left=`${r+(i?a-p-o:p)+(i?t-h-c:h)/t*o}px`,l.transform=e.style.transform}})}static _convertPoints(e,t){if(!t)return e;let i=t.x||0,a=t.y||0,s=t.width&&t.downScaledWidth?t.width/t.downScaledWidth:1;t=t.height&&t.downScaledHeight?t.height/t.downScaledHeight:1;for(let r of e)r.x=r.x*s+i,r.y=r.y*t+a;return e}_scanFrame(){!this._active||this.$video.paused||this.$video.ended||("requestVideoFrameCallback"in this.$video?this.$video.requestVideoFrameCallback.bind(this.$video):requestAnimationFrame)(()=>m(this,null,function*(){if(!(1>=this.$video.readyState)){var e=Date.now()-this._lastScanTimestamp,t=1e3/this._maxScansPerSecond;e<t&&(yield new Promise(a=>setTimeout(a,t-e))),this._lastScanTimestamp=Date.now();try{var i=yield n.scanImage(this.$video,{scanRegion:this._scanRegion,qrEngine:this._qrEnginePromise,canvas:this.$canvas})}catch(a){if(!this._active)return;this._onDecodeError(a)}!n._disableBarcodeDetector||(yield this._qrEnginePromise)instanceof Worker||(this._qrEnginePromise=n.createQrEngine()),i?(this._onDecode?this._onDecode(i):this._legacyOnDecode&&this._legacyOnDecode(i.data),this.$codeOutlineHighlight&&(clearTimeout(this._codeOutlineHighlightRemovalTimeout),this._codeOutlineHighlightRemovalTimeout=void 0,this.$codeOutlineHighlight.setAttribute("viewBox",`${this._scanRegion.x||0} ${this._scanRegion.y||0} ${this._scanRegion.width||this.$video.videoWidth} ${this._scanRegion.height||this.$video.videoHeight}`),this.$codeOutlineHighlight.firstElementChild.setAttribute("points",i.cornerPoints.map(({x:a,y:s})=>`${a},${s}`).join(" ")),this.$codeOutlineHighlight.style.display="")):this.$codeOutlineHighlight&&!this._codeOutlineHighlightRemovalTimeout&&(this._codeOutlineHighlightRemovalTimeout=setTimeout(()=>this.$codeOutlineHighlight.style.display="none",100))}this._scanFrame()}))}_onDecodeError(e){e!==n.NO_QR_CODE_FOUND&&console.log(e)}_getCameraStream(){return m(this,null,function*(){if(!navigator.mediaDevices)throw"Camera not found.";let e=/^(environment|user)$/.test(this._preferredCamera)?"facingMode":"deviceId",t=[{width:{min:1024}},{width:{min:768}},{}],i=t.map(a=>Object.assign({},a,{[e]:{exact:this._preferredCamera}}));for(let a of[...i,...t])try{let s=yield navigator.mediaDevices.getUserMedia({video:a,audio:!1}),r=this._getFacingMode(s)||(a.facingMode?this._preferredCamera:this._preferredCamera==="environment"?"user":"environment");return{stream:s,facingMode:r}}catch(s){}throw"Camera not found."})}_restartVideoStream(){return m(this,null,function*(){let e=this._paused;(yield this.pause(!0))&&!e&&this._active&&(yield this.start())})}static _stopVideoStream(e){for(let t of e.getTracks())t.stop(),e.removeTrack(t)}_setVideoMirror(e){this.$video.style.transform="scaleX("+(e==="user"?-1:1)+")"}_getFacingMode(e){return(e=e.getVideoTracks()[0])?/rear|back|environment/i.test(e.label)?"environment":/front|user|face/i.test(e.label)?"user":null:null}static _drawToCanvas(e,t,i,a=!1){i=i||document.createElement("canvas");let s=t&&t.x?t.x:0,r=t&&t.y?t.y:0,d=t&&t.width?t.width:e.videoWidth||e.width,c=t&&t.height?t.height:e.videoHeight||e.height;return a||(a=t&&t.downScaledWidth?t.downScaledWidth:d,t=t&&t.downScaledHeight?t.downScaledHeight:c,i.width!==a&&(i.width=a),i.height!==t&&(i.height=t)),t=i.getContext("2d",{alpha:!1}),t.imageSmoothingEnabled=!1,t.drawImage(e,s,r,d,c,0,0,i.width,i.height),[i,t]}static _loadImage(e){return m(this,null,function*(){if(e instanceof Image)return yield n._awaitImageLoad(e),e;if(e instanceof HTMLVideoElement||e instanceof HTMLCanvasElement||e instanceof SVGImageElement||"OffscreenCanvas"in window&&e instanceof OffscreenCanvas||"ImageBitmap"in window&&e instanceof ImageBitmap)return e;if(e instanceof File||e instanceof Blob||e instanceof URL||typeof e=="string"){let t=new Image;t.src=e instanceof File||e instanceof Blob?URL.createObjectURL(e):e.toString();try{return yield n._awaitImageLoad(t),t}finally{(e instanceof File||e instanceof Blob)&&URL.revokeObjectURL(t.src)}}else throw"Unsupported image type."})}static _awaitImageLoad(e){return m(this,null,function*(){e.complete&&e.naturalWidth!==0||(yield new Promise((t,i)=>{let a=s=>{e.removeEventListener("load",a),e.removeEventListener("error",a),s instanceof ErrorEvent?i("Image load error"):t()};e.addEventListener("load",a),e.addEventListener("error",a)}))})}static _postWorkerMessage(e,t,i,a){return m(this,null,function*(){return n._postWorkerMessageSync(yield e,t,i,a)})}static _postWorkerMessageSync(e,t,i,a){if(!(e instanceof Worker))return-1;let s=n._workerMessageId++;return e.postMessage({id:s,type:t,data:i},a),s}}n.DEFAULT_CANVAS_SIZE=400;n.NO_QR_CODE_FOUND="No QR code found";n._disableBarcodeDetector=!1;n._workerMessageId=0;export{n as e};
