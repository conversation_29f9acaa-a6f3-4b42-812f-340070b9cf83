var pt=Object.defineProperty,vt=Object.defineProperties;var yt=Object.getOwnPropertyDescriptors;var re=Object.getOwnPropertySymbols;var De=Object.prototype.hasOwnProperty,Ie=Object.prototype.propertyIsEnumerable;var Pe=(e,n,t)=>n in e?pt(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t,E=(e,n)=>{for(var t in n||(n={}))De.call(n,t)&&Pe(e,t,n[t]);if(re)for(var t of re(n))Ie.call(n,t)&&Pe(e,t,n[t]);return e},fe=(e,n)=>vt(e,yt(n));var Te=(e,n)=>{var t={};for(var s in e)De.call(e,s)&&n.indexOf(s)<0&&(t[s]=e[s]);if(e!=null&&re)for(var s of re(e))n.indexOf(s)<0&&Ie.call(e,s)&&(t[s]=e[s]);return t};var bt=(e,n)=>()=>(n||e((n={exports:{}}).exports,n),n.exports);var T=(e,n,t)=>new Promise((s,i)=>{var a=l=>{try{d(t.next(l))}catch(c){i(c)}},o=l=>{try{d(t.throw(l))}catch(c){i(c)}},d=l=>l.done?s(l.value):Promise.resolve(l.value).then(a,o);d((t=t.apply(e,n)).next())});import{r as f,a as jt,R as wt,g as Nt,b as Ne}from"./vendor-ccbdd736.js";import{l as St}from"./socket-e2a77347.js";import{e as Me}from"./qr-3e38ac8d.js";var Fr=bt(O=>{(function(){const n=document.createElement("link").relList;if(n&&n.supports&&n.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))s(i);new MutationObserver(i=>{for(const a of i)if(a.type==="childList")for(const o of a.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&s(o)}).observe(document,{childList:!0,subtree:!0});function t(i){const a={};return i.integrity&&(a.integrity=i.integrity),i.referrerPolicy&&(a.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?a.credentials="include":i.crossOrigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function s(i){if(i.ep)return;i.ep=!0;const a=t(i);fetch(i.href,a)}})();var Je={exports:{}},ce={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ct=f,kt=Symbol.for("react.element"),Et=Symbol.for("react.fragment"),Rt=Object.prototype.hasOwnProperty,Pt=Ct.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Dt={key:!0,ref:!0,__self:!0,__source:!0};function Ke(e,n,t){var s,i={},a=null,o=null;t!==void 0&&(a=""+t),n.key!==void 0&&(a=""+n.key),n.ref!==void 0&&(o=n.ref);for(s in n)Rt.call(n,s)&&!Dt.hasOwnProperty(s)&&(i[s]=n[s]);if(e&&e.defaultProps)for(s in n=e.defaultProps,n)i[s]===void 0&&(i[s]=n[s]);return{$$typeof:kt,type:e,key:a,ref:o,props:i,_owner:Pt.current}}ce.Fragment=Et;ce.jsx=Ke;ce.jsxs=Ke;Je.exports=ce;var r=Je.exports,ve={},Le=jt;ve.createRoot=Le.createRoot,ve.hydrateRoot=Le.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Q(){return Q=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s])}return e},Q.apply(this,arguments)}var z;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(z||(z={}));const Oe="popstate";function It(e){e===void 0&&(e={});function n(s,i){let{pathname:a,search:o,hash:d}=s.location;return ye("",{pathname:a,search:o,hash:d},i.state&&i.state.usr||null,i.state&&i.state.key||"default")}function t(s,i){return typeof i=="string"?i:ie(i)}return Mt(n,t,null,e)}function P(e,n){if(e===!1||e===null||typeof e=="undefined")throw new Error(n)}function Ge(e,n){if(!e){typeof console!="undefined"&&console.warn(n);try{throw new Error(n)}catch(t){}}}function Tt(){return Math.random().toString(36).substr(2,8)}function $e(e,n){return{usr:e.state,key:e.key,idx:n}}function ye(e,n,t,s){return t===void 0&&(t=null),Q({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof n=="string"?K(n):n,{state:t,key:n&&n.key||s||Tt()})}function ie(e){let{pathname:n="/",search:t="",hash:s=""}=e;return t&&t!=="?"&&(n+=t.charAt(0)==="?"?t:"?"+t),s&&s!=="#"&&(n+=s.charAt(0)==="#"?s:"#"+s),n}function K(e){let n={};if(e){let t=e.indexOf("#");t>=0&&(n.hash=e.substr(t),e=e.substr(0,t));let s=e.indexOf("?");s>=0&&(n.search=e.substr(s),e=e.substr(0,s)),e&&(n.pathname=e)}return n}function Mt(e,n,t,s){s===void 0&&(s={});let{window:i=document.defaultView,v5Compat:a=!1}=s,o=i.history,d=z.Pop,l=null,c=h();c==null&&(c=0,o.replaceState(Q({},o.state,{idx:c}),""));function h(){return(o.state||{idx:null}).idx}function u(){d=z.Pop;let m=h(),b=m==null?null:m-c;c=m,l&&l({action:d,location:x.location,delta:b})}function g(m,b){d=z.Push;let p=ye(x.location,m,b);t&&t(p,m),c=h()+1;let j=$e(p,c),R=x.createHref(p);try{o.pushState(j,"",R)}catch(D){if(D instanceof DOMException&&D.name==="DataCloneError")throw D;i.location.assign(R)}a&&l&&l({action:d,location:x.location,delta:1})}function v(m,b){d=z.Replace;let p=ye(x.location,m,b);t&&t(p,m),c=h();let j=$e(p,c),R=x.createHref(p);o.replaceState(j,"",R),a&&l&&l({action:d,location:x.location,delta:0})}function y(m){let b=i.location.origin!=="null"?i.location.origin:i.location.href,p=typeof m=="string"?m:ie(m);return p=p.replace(/ $/,"%20"),P(b,"No window.location.(origin|href) available to create URL for href: "+p),new URL(p,b)}let x={get action(){return d},get location(){return e(i,o)},listen(m){if(l)throw new Error("A history only accepts one active listener");return i.addEventListener(Oe,u),l=m,()=>{i.removeEventListener(Oe,u),l=null}},createHref(m){return n(i,m)},createURL:y,encodeLocation(m){let b=y(m);return{pathname:b.pathname,search:b.search,hash:b.hash}},push:g,replace:v,go(m){return o.go(m)}};return x}var Ae;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Ae||(Ae={}));function Lt(e,n,t){return t===void 0&&(t="/"),Ot(e,n,t,!1)}function Ot(e,n,t,s){let i=typeof n=="string"?K(n):n,a=Se(i.pathname||"/",t);if(a==null)return null;let o=Ze(e);$t(o);let d=null;for(let l=0;d==null&&l<o.length;++l){let c=Jt(a);d=Vt(o[l],c,s)}return d}function Ze(e,n,t,s){n===void 0&&(n=[]),t===void 0&&(t=[]),s===void 0&&(s="");let i=(a,o,d)=>{let l={relativePath:d===void 0?a.path||"":d,caseSensitive:a.caseSensitive===!0,childrenIndex:o,route:a};l.relativePath.startsWith("/")&&(P(l.relativePath.startsWith(s),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+s+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(s.length));let c=F([s,l.relativePath]),h=t.concat(l);a.children&&a.children.length>0&&(P(a.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+c+'".')),Ze(a.children,n,h,c)),!(a.path==null&&!a.index)&&n.push({path:c,score:Wt(c,a.index),routesMeta:h})};return e.forEach((a,o)=>{var d;if(a.path===""||!((d=a.path)!=null&&d.includes("?")))i(a,o);else for(let l of Qe(a.path))i(a,o,l)}),n}function Qe(e){let n=e.split("/");if(n.length===0)return[];let[t,...s]=n,i=t.endsWith("?"),a=t.replace(/\?$/,"");if(s.length===0)return i?[a,""]:[a];let o=Qe(s.join("/")),d=[];return d.push(...o.map(l=>l===""?a:[a,l].join("/"))),i&&d.push(...o),d.map(l=>e.startsWith("/")&&l===""?"/":l)}function $t(e){e.sort((n,t)=>n.score!==t.score?t.score-n.score:Ht(n.routesMeta.map(s=>s.childrenIndex),t.routesMeta.map(s=>s.childrenIndex)))}const At=/^:[\w-]+$/,Ut=3,_t=2,Bt=1,zt=10,Ft=-2,Ue=e=>e==="*";function Wt(e,n){let t=e.split("/"),s=t.length;return t.some(Ue)&&(s+=Ft),n&&(s+=_t),t.filter(i=>!Ue(i)).reduce((i,a)=>i+(At.test(a)?Ut:a===""?Bt:zt),s)}function Ht(e,n){return e.length===n.length&&e.slice(0,-1).every((s,i)=>s===n[i])?e[e.length-1]-n[n.length-1]:0}function Vt(e,n,t){t===void 0&&(t=!1);let{routesMeta:s}=e,i={},a="/",o=[];for(let d=0;d<s.length;++d){let l=s[d],c=d===s.length-1,h=a==="/"?n:n.slice(a.length)||"/",u=_e({path:l.relativePath,caseSensitive:l.caseSensitive,end:c},h),g=l.route;if(!u&&c&&t&&!s[s.length-1].route.index&&(u=_e({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},h)),!u)return null;Object.assign(i,u.params),o.push({params:i,pathname:F([a,u.pathname]),pathnameBase:Qt(F([a,u.pathnameBase])),route:g}),u.pathnameBase!=="/"&&(a=F([a,u.pathnameBase]))}return o}function _e(e,n){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[t,s]=qt(e.path,e.caseSensitive,e.end),i=n.match(t);if(!i)return null;let a=i[0],o=a.replace(/(.)\/+$/,"$1"),d=i.slice(1);return{params:s.reduce((c,h,u)=>{let{paramName:g,isOptional:v}=h;if(g==="*"){let x=d[u]||"";o=a.slice(0,a.length-x.length).replace(/(.)\/+$/,"$1")}const y=d[u];return v&&!y?c[g]=void 0:c[g]=(y||"").replace(/%2F/g,"/"),c},{}),pathname:a,pathnameBase:o,pattern:e}}function qt(e,n,t){n===void 0&&(n=!1),t===void 0&&(t=!0),Ge(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let s=[],i="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(o,d,l)=>(s.push({paramName:d,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(s.push({paramName:"*"}),i+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):t?i+="\\/*$":e!==""&&e!=="/"&&(i+="(?:(?=\\/|$))"),[new RegExp(i,n?void 0:"i"),s]}function Jt(e){try{return e.split("/").map(n=>decodeURIComponent(n).replace(/\//g,"%2F")).join("/")}catch(n){return Ge(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+n+").")),e}}function Se(e,n){if(n==="/")return e;if(!e.toLowerCase().startsWith(n.toLowerCase()))return null;let t=n.endsWith("/")?n.length-1:n.length,s=e.charAt(t);return s&&s!=="/"?null:e.slice(t)||"/"}function Kt(e,n){n===void 0&&(n="/");let{pathname:t,search:s="",hash:i=""}=typeof e=="string"?K(e):e;return{pathname:t?t.startsWith("/")?t:Gt(t,n):n,search:Xt(s),hash:Yt(i)}}function Gt(e,n){let t=n.replace(/\/+$/,"").split("/");return e.split("/").forEach(i=>{i===".."?t.length>1&&t.pop():i!=="."&&t.push(i)}),t.length>1?t.join("/"):"/"}function ge(e,n,t,s){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+n+"` field ["+JSON.stringify(s)+"].  Please separate it out to the ")+("`to."+t+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Zt(e){return e.filter((n,t)=>t===0||n.route.path&&n.route.path.length>0)}function Xe(e,n){let t=Zt(e);return n?t.map((s,i)=>i===t.length-1?s.pathname:s.pathnameBase):t.map(s=>s.pathnameBase)}function Ye(e,n,t,s){s===void 0&&(s=!1);let i;typeof e=="string"?i=K(e):(i=Q({},e),P(!i.pathname||!i.pathname.includes("?"),ge("?","pathname","search",i)),P(!i.pathname||!i.pathname.includes("#"),ge("#","pathname","hash",i)),P(!i.search||!i.search.includes("#"),ge("#","search","hash",i)));let a=e===""||i.pathname==="",o=a?"/":i.pathname,d;if(o==null)d=t;else{let u=n.length-1;if(!s&&o.startsWith("..")){let g=o.split("/");for(;g[0]==="..";)g.shift(),u-=1;i.pathname=g.join("/")}d=u>=0?n[u]:"/"}let l=Kt(i,d),c=o&&o!=="/"&&o.endsWith("/"),h=(a||o===".")&&t.endsWith("/");return!l.pathname.endsWith("/")&&(c||h)&&(l.pathname+="/"),l}const F=e=>e.join("/").replace(/\/\/+/g,"/"),Qt=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Xt=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Yt=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function en(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const et=["post","put","patch","delete"];new Set(et);const tn=["get",...et];new Set(tn);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function X(){return X=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s])}return e},X.apply(this,arguments)}const Ce=f.createContext(null),nn=f.createContext(null),H=f.createContext(null),de=f.createContext(null),V=f.createContext({outlet:null,matches:[],isDataRoute:!1}),tt=f.createContext(null);function rn(e,n){let{relative:t}=n===void 0?{}:n;ee()||P(!1);let{basename:s,navigator:i}=f.useContext(H),{hash:a,pathname:o,search:d}=rt(e,{relative:t}),l=o;return s!=="/"&&(l=o==="/"?s:F([s,o])),i.createHref({pathname:l,search:d,hash:a})}function ee(){return f.useContext(de)!=null}function te(){return ee()||P(!1),f.useContext(de).location}function nt(e){f.useContext(H).static||f.useLayoutEffect(e)}function sn(){let{isDataRoute:e}=f.useContext(V);return e?vn():an()}function an(){ee()||P(!1);let e=f.useContext(Ce),{basename:n,future:t,navigator:s}=f.useContext(H),{matches:i}=f.useContext(V),{pathname:a}=te(),o=JSON.stringify(Xe(i,t.v7_relativeSplatPath)),d=f.useRef(!1);return nt(()=>{d.current=!0}),f.useCallback(function(c,h){if(h===void 0&&(h={}),!d.current)return;if(typeof c=="number"){s.go(c);return}let u=Ye(c,JSON.parse(o),a,h.relative==="path");e==null&&n!=="/"&&(u.pathname=u.pathname==="/"?n:F([n,u.pathname])),(h.replace?s.replace:s.push)(u,h.state,h)},[n,s,o,a,e])}function rt(e,n){let{relative:t}=n===void 0?{}:n,{future:s}=f.useContext(H),{matches:i}=f.useContext(V),{pathname:a}=te(),o=JSON.stringify(Xe(i,s.v7_relativeSplatPath));return f.useMemo(()=>Ye(e,JSON.parse(o),a,t==="path"),[e,o,a,t])}function on(e,n){return ln(e,n)}function ln(e,n,t,s){ee()||P(!1);let{navigator:i}=f.useContext(H),{matches:a}=f.useContext(V),o=a[a.length-1],d=o?o.params:{};o&&o.pathname;let l=o?o.pathnameBase:"/";o&&o.route;let c=te(),h;if(n){var u;let m=typeof n=="string"?K(n):n;l==="/"||(u=m.pathname)!=null&&u.startsWith(l)||P(!1),h=m}else h=c;let g=h.pathname||"/",v=g;if(l!=="/"){let m=l.replace(/^\//,"").split("/");v="/"+g.replace(/^\//,"").split("/").slice(m.length).join("/")}let y=Lt(e,{pathname:v}),x=mn(y&&y.map(m=>Object.assign({},m,{params:Object.assign({},d,m.params),pathname:F([l,i.encodeLocation?i.encodeLocation(m.pathname).pathname:m.pathname]),pathnameBase:m.pathnameBase==="/"?l:F([l,i.encodeLocation?i.encodeLocation(m.pathnameBase).pathname:m.pathnameBase])})),a,t,s);return n&&x?f.createElement(de.Provider,{value:{location:X({pathname:"/",search:"",hash:"",state:null,key:"default"},h),navigationType:z.Pop}},x):x}function cn(){let e=pn(),n=en(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),t=e instanceof Error?e.stack:null,i={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"},a=null;return f.createElement(f.Fragment,null,f.createElement("h2",null,"Unexpected Application Error!"),f.createElement("h3",{style:{fontStyle:"italic"}},n),t?f.createElement("pre",{style:i},t):null,a)}const dn=f.createElement(cn,null);class un extends f.Component{constructor(n){super(n),this.state={location:n.location,revalidation:n.revalidation,error:n.error}}static getDerivedStateFromError(n){return{error:n}}static getDerivedStateFromProps(n,t){return t.location!==n.location||t.revalidation!=="idle"&&n.revalidation==="idle"?{error:n.error,location:n.location,revalidation:n.revalidation}:{error:n.error!==void 0?n.error:t.error,location:t.location,revalidation:n.revalidation||t.revalidation}}componentDidCatch(n,t){console.error("React Router caught the following error during render",n,t)}render(){return this.state.error!==void 0?f.createElement(V.Provider,{value:this.props.routeContext},f.createElement(tt.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function hn(e){let{routeContext:n,match:t,children:s}=e,i=f.useContext(Ce);return i&&i.static&&i.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=t.route.id),f.createElement(V.Provider,{value:n},s)}function mn(e,n,t,s){var i;if(n===void 0&&(n=[]),t===void 0&&(t=null),s===void 0&&(s=null),e==null){var a;if(!t)return null;if(t.errors)e=t.matches;else if((a=s)!=null&&a.v7_partialHydration&&n.length===0&&!t.initialized&&t.matches.length>0)e=t.matches;else return null}let o=e,d=(i=t)==null?void 0:i.errors;if(d!=null){let h=o.findIndex(u=>u.route.id&&(d==null?void 0:d[u.route.id])!==void 0);h>=0||P(!1),o=o.slice(0,Math.min(o.length,h+1))}let l=!1,c=-1;if(t&&s&&s.v7_partialHydration)for(let h=0;h<o.length;h++){let u=o[h];if((u.route.HydrateFallback||u.route.hydrateFallbackElement)&&(c=h),u.route.id){let{loaderData:g,errors:v}=t,y=u.route.loader&&g[u.route.id]===void 0&&(!v||v[u.route.id]===void 0);if(u.route.lazy||y){l=!0,c>=0?o=o.slice(0,c+1):o=[o[0]];break}}}return o.reduceRight((h,u,g)=>{let v,y=!1,x=null,m=null;t&&(v=d&&u.route.id?d[u.route.id]:void 0,x=u.route.errorElement||dn,l&&(c<0&&g===0?(yn("route-fallback",!1),y=!0,m=null):c===g&&(y=!0,m=u.route.hydrateFallbackElement||null)));let b=n.concat(o.slice(0,g+1)),p=()=>{let j;return v?j=x:y?j=m:u.route.Component?j=f.createElement(u.route.Component,null):u.route.element?j=u.route.element:j=h,f.createElement(hn,{match:u,routeContext:{outlet:h,matches:b,isDataRoute:t!=null},children:j})};return t&&(u.route.ErrorBoundary||u.route.errorElement||g===0)?f.createElement(un,{location:t.location,revalidation:t.revalidation,component:x,error:v,children:p(),routeContext:{outlet:null,matches:b,isDataRoute:!0}}):p()},null)}var st=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(st||{}),ae=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(ae||{});function fn(e){let n=f.useContext(Ce);return n||P(!1),n}function gn(e){let n=f.useContext(nn);return n||P(!1),n}function xn(e){let n=f.useContext(V);return n||P(!1),n}function it(e){let n=xn(),t=n.matches[n.matches.length-1];return t.route.id||P(!1),t.route.id}function pn(){var e;let n=f.useContext(tt),t=gn(ae.UseRouteError),s=it(ae.UseRouteError);return n!==void 0?n:(e=t.errors)==null?void 0:e[s]}function vn(){let{router:e}=fn(st.UseNavigateStable),n=it(ae.UseNavigateStable),t=f.useRef(!1);return nt(()=>{t.current=!0}),f.useCallback(function(i,a){a===void 0&&(a={}),t.current&&(typeof i=="number"?e.navigate(i):e.navigate(i,X({fromRouteId:n},a)))},[e,n])}const Be={};function yn(e,n,t){!n&&!Be[e]&&(Be[e]=!0)}function bn(e,n){e==null||e.v7_startTransition,(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!n||n.v7_relativeSplatPath),n&&(n.v7_fetcherPersist,n.v7_normalizeFormMethod,n.v7_partialHydration,n.v7_skipActionErrorRevalidation)}function Z(e){P(!1)}function jn(e){let{basename:n="/",children:t=null,location:s,navigationType:i=z.Pop,navigator:a,static:o=!1,future:d}=e;ee()&&P(!1);let l=n.replace(/^\/*/,"/"),c=f.useMemo(()=>({basename:l,navigator:a,static:o,future:X({v7_relativeSplatPath:!1},d)}),[l,d,a,o]);typeof s=="string"&&(s=K(s));let{pathname:h="/",search:u="",hash:g="",state:v=null,key:y="default"}=s,x=f.useMemo(()=>{let m=Se(h,l);return m==null?null:{location:{pathname:m,search:u,hash:g,state:v,key:y},navigationType:i}},[l,h,u,g,v,y,i]);return x==null?null:f.createElement(H.Provider,{value:c},f.createElement(de.Provider,{children:t,value:x}))}function wn(e){let{children:n,location:t}=e;return on(be(n),t)}new Promise(()=>{});function be(e,n){n===void 0&&(n=[]);let t=[];return f.Children.forEach(e,(s,i)=>{if(!f.isValidElement(s))return;let a=[...n,i];if(s.type===f.Fragment){t.push.apply(t,be(s.props.children,a));return}s.type!==Z&&P(!1),!s.props.index||!s.props.children||P(!1);let o={id:s.props.id||a.join("-"),caseSensitive:s.props.caseSensitive,element:s.props.element,Component:s.props.Component,index:s.props.index,path:s.props.path,loader:s.props.loader,action:s.props.action,errorElement:s.props.errorElement,ErrorBoundary:s.props.ErrorBoundary,hasErrorBoundary:s.props.ErrorBoundary!=null||s.props.errorElement!=null,shouldRevalidate:s.props.shouldRevalidate,handle:s.props.handle,lazy:s.props.lazy};s.props.children&&(o.children=be(s.props.children,a)),t.push(o)}),t}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function je(){return je=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s])}return e},je.apply(this,arguments)}function Nn(e,n){if(e==null)return{};var t={},s=Object.keys(e),i,a;for(a=0;a<s.length;a++)i=s[a],!(n.indexOf(i)>=0)&&(t[i]=e[i]);return t}function Sn(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function Cn(e,n){return e.button===0&&(!n||n==="_self")&&!Sn(e)}const kn=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],En="6";try{window.__reactRouterVersion=En}catch(e){}const Rn="startTransition",ze=wt[Rn];function Pn(e){let{basename:n,children:t,future:s,window:i}=e,a=f.useRef();a.current==null&&(a.current=It({window:i,v5Compat:!0}));let o=a.current,[d,l]=f.useState({action:o.action,location:o.location}),{v7_startTransition:c}=s||{},h=f.useCallback(u=>{c&&ze?ze(()=>l(u)):l(u)},[l,c]);return f.useLayoutEffect(()=>o.listen(h),[o,h]),f.useEffect(()=>bn(s),[s]),f.createElement(jn,{basename:n,children:t,location:d.location,navigationType:d.action,navigator:o,future:s})}const Dn=typeof window!="undefined"&&typeof window.document!="undefined"&&typeof window.document.createElement!="undefined",In=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,se=f.forwardRef(function(n,t){let{onClick:s,relative:i,reloadDocument:a,replace:o,state:d,target:l,to:c,preventScrollReset:h,viewTransition:u}=n,g=Nn(n,kn),{basename:v}=f.useContext(H),y,x=!1;if(typeof c=="string"&&In.test(c)&&(y=c,Dn))try{let j=new URL(window.location.href),R=c.startsWith("//")?new URL(j.protocol+c):new URL(c),D=Se(R.pathname,v);R.origin===j.origin&&D!=null?c=D+R.search+R.hash:x=!0}catch(j){}let m=rn(c,{relative:i}),b=Tn(c,{replace:o,state:d,target:l,preventScrollReset:h,relative:i,viewTransition:u});function p(j){s&&s(j),j.defaultPrevented||b(j)}return f.createElement("a",je({},g,{href:y||m,onClick:x||a?s:p,ref:t,target:l}))});var Fe;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Fe||(Fe={}));var We;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(We||(We={}));function Tn(e,n){let{target:t,replace:s,state:i,preventScrollReset:a,relative:o,viewTransition:d}=n===void 0?{}:n,l=sn(),c=te(),h=rt(e,{relative:o});return f.useCallback(u=>{if(Cn(u,t)){u.preventDefault();let g=s!==void 0?s:ie(c)===ie(h);l(e,{replace:g,state:i,preventScrollReset:a,relative:o,viewTransition:d})}},[c,l,h,s,i,t,e,a,o,d])}const He=e=>{let n;const t=new Set,s=(h,u)=>{const g=typeof h=="function"?h(n):h;if(!Object.is(g,n)){const v=n;n=(u!=null?u:typeof g!="object"||g===null)?g:Object.assign({},n,g),t.forEach(y=>y(n,v))}},i=()=>n,l={setState:s,getState:i,getInitialState:()=>c,subscribe:h=>(t.add(h),()=>t.delete(h)),destroy:()=>{t.clear()}},c=n=e(s,i,l);return l},Mn=e=>e?He(e):He;var at={exports:{}},ot={},lt={exports:{}},ct={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var J=f;function Ln(e,n){return e===n&&(e!==0||1/e===1/n)||e!==e&&n!==n}var On=typeof Object.is=="function"?Object.is:Ln,$n=J.useState,An=J.useEffect,Un=J.useLayoutEffect,_n=J.useDebugValue;function Bn(e,n){var t=n(),s=$n({inst:{value:t,getSnapshot:n}}),i=s[0].inst,a=s[1];return Un(function(){i.value=t,i.getSnapshot=n,xe(i)&&a({inst:i})},[e,t,n]),An(function(){return xe(i)&&a({inst:i}),e(function(){xe(i)&&a({inst:i})})},[e]),_n(t),t}function xe(e){var n=e.getSnapshot;e=e.value;try{var t=n();return!On(e,t)}catch(s){return!0}}function zn(e,n){return n()}var Fn=typeof window=="undefined"||typeof window.document=="undefined"||typeof window.document.createElement=="undefined"?zn:Bn;ct.useSyncExternalStore=J.useSyncExternalStore!==void 0?J.useSyncExternalStore:Fn;lt.exports=ct;var Wn=lt.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ue=f,Hn=Wn;function Vn(e,n){return e===n&&(e!==0||1/e===1/n)||e!==e&&n!==n}var qn=typeof Object.is=="function"?Object.is:Vn,Jn=Hn.useSyncExternalStore,Kn=ue.useRef,Gn=ue.useEffect,Zn=ue.useMemo,Qn=ue.useDebugValue;ot.useSyncExternalStoreWithSelector=function(e,n,t,s,i){var a=Kn(null);if(a.current===null){var o={hasValue:!1,value:null};a.current=o}else o=a.current;a=Zn(function(){function l(v){if(!c){if(c=!0,h=v,v=s(v),i!==void 0&&o.hasValue){var y=o.value;if(i(y,v))return u=y}return u=v}if(y=u,qn(h,v))return y;var x=s(v);return i!==void 0&&i(y,x)?(h=v,y):(h=v,u=x)}var c=!1,h,u,g=t===void 0?null:t;return[function(){return l(n())},g===null?void 0:function(){return l(g())}]},[n,t,s,i]);var d=Jn(e,a[0],a[1]);return Gn(function(){o.hasValue=!0,o.value=d},[d]),Qn(d),d};at.exports=ot;var Xn=at.exports;const Yn=Nt(Xn),{useDebugValue:er}=Ne,{useSyncExternalStoreWithSelector:tr}=Yn,nr=e=>e;function rr(e,n=nr,t){const s=tr(e.subscribe,e.getState,e.getServerState||e.getInitialState,n,t);return er(s),s}const Ve=e=>{const n=typeof e=="function"?Mn(e):e,t=(s,i)=>rr(n,s,i);return Object.assign(t,n),t},ke=e=>e?Ve(e):Ve,sr=e=>(n,t,s)=>{const i=s.subscribe;return s.subscribe=(o,d,l)=>{let c=o;if(d){const h=(l==null?void 0:l.equalityFn)||Object.is;let u=o(s.getState());c=g=>{const v=o(g);if(!h(u,v)){const y=u;d(u=v,y)}},l!=null&&l.fireImmediately&&d(u,u)}return i(c)},e(n,t,s)},dt=sr;function ir(e,n){let t;try{t=e()}catch(i){return}return{getItem:i=>{var a;const o=l=>l===null?null:JSON.parse(l,n==null?void 0:n.reviver),d=(a=t.getItem(i))!=null?a:null;return d instanceof Promise?d.then(o):o(d)},setItem:(i,a)=>t.setItem(i,JSON.stringify(a,n==null?void 0:n.replacer)),removeItem:i=>t.removeItem(i)}}const Y=e=>n=>{try{const t=e(n);return t instanceof Promise?t:{then(s){return Y(s)(t)},catch(s){return this}}}catch(t){return{then(s){return this},catch(s){return Y(s)(t)}}}},ar=(e,n)=>(t,s,i)=>{let a=E({getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:m=>m,version:0,merge:(m,b)=>E(E({},b),m)},n),o=!1;const d=new Set,l=new Set;let c;try{c=a.getStorage()}catch(m){}if(!c)return e((...m)=>{console.warn(`[zustand persist middleware] Unable to update item '${a.name}', the given storage is currently unavailable.`),t(...m)},s,i);const h=Y(a.serialize),u=()=>{const m=a.partialize(E({},s()));let b;const p=h({state:m,version:a.version}).then(j=>c.setItem(a.name,j)).catch(j=>{b=j});if(b)throw b;return p},g=i.setState;i.setState=(m,b)=>{g(m,b),u()};const v=e((...m)=>{t(...m),u()},s,i);let y;const x=()=>{var m;if(!c)return;o=!1,d.forEach(p=>p(s()));const b=((m=a.onRehydrateStorage)==null?void 0:m.call(a,s()))||void 0;return Y(c.getItem.bind(c))(a.name).then(p=>{if(p)return a.deserialize(p)}).then(p=>{if(p)if(typeof p.version=="number"&&p.version!==a.version){if(a.migrate)return a.migrate(p.state,p.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return p.state}).then(p=>{var j;return y=a.merge(p,(j=s())!=null?j:v),t(y,!0),u()}).then(()=>{b==null||b(y,void 0),o=!0,l.forEach(p=>p(y))}).catch(p=>{b==null||b(void 0,p)})};return i.persist={setOptions:m=>{a=E(E({},a),m),m.getStorage&&(c=m.getStorage())},clearStorage:()=>{c==null||c.removeItem(a.name)},getOptions:()=>a,rehydrate:()=>x(),hasHydrated:()=>o,onHydrate:m=>(d.add(m),()=>{d.delete(m)}),onFinishHydration:m=>(l.add(m),()=>{l.delete(m)})},x(),y||v},or=(e,n)=>(t,s,i)=>{let a=E({storage:ir(()=>localStorage),partialize:x=>x,version:0,merge:(x,m)=>E(E({},m),x)},n),o=!1;const d=new Set,l=new Set;let c=a.storage;if(!c)return e((...x)=>{console.warn(`[zustand persist middleware] Unable to update item '${a.name}', the given storage is currently unavailable.`),t(...x)},s,i);const h=()=>{const x=a.partialize(E({},s()));return c.setItem(a.name,{state:x,version:a.version})},u=i.setState;i.setState=(x,m)=>{u(x,m),h()};const g=e((...x)=>{t(...x),h()},s,i);i.getInitialState=()=>g;let v;const y=()=>{var x,m;if(!c)return;o=!1,d.forEach(p=>{var j;return p((j=s())!=null?j:g)});const b=((m=a.onRehydrateStorage)==null?void 0:m.call(a,(x=s())!=null?x:g))||void 0;return Y(c.getItem.bind(c))(a.name).then(p=>{if(p)if(typeof p.version=="number"&&p.version!==a.version){if(a.migrate)return[!0,a.migrate(p.state,p.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,p.state];return[!1,void 0]}).then(p=>{var j;const[R,D]=p;if(v=a.merge(D,(j=s())!=null?j:g),t(v,!0),R)return h()}).then(()=>{b==null||b(v,void 0),v=s(),o=!0,l.forEach(p=>p(v))}).catch(p=>{b==null||b(void 0,p)})};return i.persist={setOptions:x=>{a=E(E({},a),x),x.storage&&(c=x.storage)},clearStorage:()=>{c==null||c.removeItem(a.name)},getOptions:()=>a,rehydrate:()=>y(),hasHydrated:()=>o,onHydrate:x=>(d.add(x),()=>{d.delete(x)}),onFinishHydration:x=>(l.add(x),()=>{l.delete(x)})},a.skipHydration||y(),v||g},lr=(e,n)=>"getStorage"in n||"serialize"in n||"deserialize"in n?ar(e,n):or(e,n),cr=lr,dr={}.VITE_SOCKET_URL||"ws://localhost:3001",W=ke()(dt((e,n)=>({socket:null,isConnected:!1,isConnecting:!1,connectionError:null,lastPing:null,reconnectAttempts:0,maxReconnectAttempts:5,connect:()=>{const{isConnected:t,isConnecting:s}=n();if(!(t||s)){e({isConnecting:!0,connectionError:null});try{const i=St(dr,{transports:["websocket","polling"],timeout:1e4,reconnection:!0,reconnectionAttempts:5,reconnectionDelay:1e3,reconnectionDelayMax:5e3});i.on("connect",()=>{console.log("Socket connected:",i.id),e({socket:i,isConnected:!0,isConnecting:!1,connectionError:null,reconnectAttempts:0})}),i.on("connect_error",a=>{console.error("Socket connection error:",a),e(o=>({isConnecting:!1,connectionError:a.message,reconnectAttempts:o.reconnectAttempts+1}))}),i.on("disconnect",a=>{console.log("Socket disconnected:",a),e({isConnected:!1,socket:null})}),i.on("pong",()=>{e({lastPing:Date.now()})}),i.on("reconnect",a=>{console.log("Socket reconnected after",a,"attempts"),e({reconnectAttempts:0})}),i.on("reconnect_attempt",a=>{console.log("Socket reconnect attempt:",a),e({reconnectAttempts:a})}),i.on("reconnect_failed",()=>{console.error("Socket reconnection failed"),e({connectionError:"重连失败，请检查网络连接",isConnecting:!1})}),e({socket:i})}catch(i){console.error("Failed to create socket:",i);const a=i instanceof Error?i.message:"Socket连接失败";e({isConnecting:!1,connectionError:a})}}},disconnect:()=>{const{socket:t}=n();t&&(t.disconnect(),e({socket:null,isConnected:!1,isConnecting:!1,connectionError:null}))},emit:(t,s)=>{const{socket:i,isConnected:a}=n();return i&&a?(i.emit(t,s),!0):(console.warn("Socket not connected, cannot emit:",t),!1)},on:(t,s)=>{const{socket:i}=n();return i?(i.on(t,s),()=>i.off(t,s)):()=>{}},off:(t,s)=>{const{socket:i}=n();i&&(s?i.off(t,s):i.off(t))},ping:()=>{const{socket:t,isConnected:s}=n();t&&s&&t.emit("ping")},getConnectionStatus:()=>{const{isConnected:t,isConnecting:s,connectionError:i,reconnectAttempts:a}=n();return t?"connected":s?"connecting":i?"error":a>0?"reconnecting":"disconnected"},resetConnection:()=>{e({connectionError:null,reconnectAttempts:0})},forceReconnect:()=>{const{disconnect:t,connect:s}=n();t(),setTimeout(()=>{s()},1e3)}})));setInterval(()=>{const{ping:e,isConnected:n}=W.getState();n&&e()},3e4);function ur(){const e=Date.now().toString(36),n=Math.random().toString(36).substr(2,9);return`mobile_${e}_${n}`}function hr(){return T(this,null,function*(){const e={name:mr(),type:"mobile",platform:navigator.platform,userAgent:navigator.userAgent,language:navigator.language,languages:navigator.languages,online:navigator.onLine,cookieEnabled:navigator.cookieEnabled,screen:{width:screen.width,height:screen.height,colorDepth:screen.colorDepth,pixelDepth:screen.pixelDepth},viewport:{width:window.innerWidth,height:window.innerHeight},timezone:Intl.DateTimeFormat().resolvedOptions().timeZone,timestamp:new Date().toISOString()};return"connection"in navigator&&navigator.connection&&(e.connection={effectiveType:navigator.connection.effectiveType,downlink:navigator.connection.downlink,rtt:navigator.connection.rtt,saveData:navigator.connection.saveData}),e})}function mr(){const e=navigator.userAgent;if(/iPad/.test(e))return"iPad";if(/iPhone/.test(e))return"iPhone";if(/iPod/.test(e))return"iPod";if(/Android/.test(e)){const n=e.match(/Android\s([0-9\.]+)/);return`Android ${n?n[1]:""}`.trim()}return/Windows Phone/.test(e)?"Windows Phone":/Mobile/.test(e)?"Mobile Device":/Chrome/.test(e)?"Chrome Browser":/Firefox/.test(e)?"Firefox Browser":/Safari/.test(e)&&!/Chrome/.test(e)?"Safari Browser":/Edge/.test(e)?"Edge Browser":"Unknown Device"}function pe(e=100){"vibrate"in navigator&&navigator.vibrate(e)}const G=ke()(cr((e,n)=>({deviceId:null,deviceName:"",deviceType:"mobile",isRegistered:!1,registrationError:null,pairedDevices:[],currentSession:null,pairingCode:"",isPairing:!1,pairingError:null,capabilities:{speechRecognition:!1,mediaRecording:!1,camera:!1,vibration:!1},initializeDevice:()=>T(O,null,function*(){try{let{deviceId:t}=n();t||(t=ur());const s=yield hr(),i=yield n().detectCapabilities();return e({deviceId:t,deviceName:s.name,deviceType:"mobile",capabilities:i}),{deviceId:t,deviceInfo:s,capabilities:i}}catch(t){console.error("设备初始化失败:",t);const s=t instanceof Error?t.message:"设备初始化失败";throw e({registrationError:s}),t}}),detectCapabilities:()=>T(O,null,function*(){const t={speechRecognition:!1,mediaRecording:!1,camera:!1,vibration:!1};try{if(t.speechRecognition="webkitSpeechRecognition"in window||"SpeechRecognition"in window,t.mediaRecording=navigator.mediaDevices&&typeof navigator.mediaDevices.getUserMedia=="function",t.mediaRecording)try{const s=yield navigator.mediaDevices.enumerateDevices();t.camera=s.some(i=>i.kind==="videoinput")}catch(s){console.warn("无法检测摄像头:",s)}t.vibration="vibrate"in navigator}catch(s){console.error("检测设备能力失败:",s)}return t}),registerDevice:t=>new Promise((s,i)=>{const{deviceId:a,deviceName:o,deviceType:d,capabilities:l}=n();if(!t){i(new Error("Socket连接不可用"));return}const c={deviceId:a,type:d,name:o,capabilities:Object.keys(l).filter(u=>l[u]),metadata:{userAgent:navigator.userAgent,platform:navigator.platform,language:navigator.language,timestamp:new Date().toISOString()}},h=u=>{u.success?(e({isRegistered:!0,registrationError:null,deviceId:u.deviceId}),s(u)):(e({isRegistered:!1,registrationError:u.error}),i(new Error(u.error))),t.off("device:registered",h)};t.on("device:registered",h),t.emit("device:register",c),setTimeout(()=>{t.off("device:registered",h),i(new Error("设备注册超时"))},1e4)}),startPairing:t=>new Promise(s=>{e({isPairing:!0,pairingError:null,pairingCode:t}),s({success:!0,pairingCode:t})}),onPairingSuccess:t=>{const{devices:s,sessionId:i}=t;e({isPairing:!1,pairingError:null,currentSession:i,pairedDevices:s.filter(a=>a.id!==n().deviceId)})},onPairingError:t=>{e({isPairing:!1,pairingError:t,pairingCode:""})},disconnectPairing:()=>{e({currentSession:null,pairedDevices:[],pairingCode:""})},updateDeviceName:t=>{e({deviceName:t})},getPairingStatus:()=>{const{currentSession:t,pairedDevices:s,isPairing:i}=n();return i?"pairing":t&&s.length>0?"paired":"unpaired"},resetDevice:()=>{e({isRegistered:!1,registrationError:null,currentSession:null,pairedDevices:[],pairingCode:"",isPairing:!1,pairingError:null})},getDeviceStats:()=>{const{deviceId:t,deviceName:s,deviceType:i,capabilities:a,pairedDevices:o,currentSession:d}=n();return{deviceId:t,deviceName:s,deviceType:i,capabilities:a,pairedDevicesCount:o.length,hasActiveSession:!!d,supportedFeatures:Object.keys(a).filter(l=>a[l])}}}),{name:"smartinput-device",partialize:e=>({deviceId:e.deviceId,deviceName:e.deviceName,deviceType:e.deviceType,capabilities:e.capabilities})}));var fr={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const gr=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),xr=(e,n)=>{const t=f.forwardRef((h,c)=>{var u=h,{color:s="currentColor",size:i=24,strokeWidth:a=2,absoluteStrokeWidth:o,children:d}=u,l=Te(u,["color","size","strokeWidth","absoluteStrokeWidth","children"]);return f.createElement("svg",E(fe(E({ref:c},fr),{width:i,height:i,stroke:s,strokeWidth:o?Number(a)*24/Number(i):a,className:`lucide lucide-${gr(e)}`}),l),[...n.map(([g,v])=>f.createElement(g,v)),...(Array.isArray(d)?d:[d])||[]])});return t.displayName=`${e}`,t};var C=xr;const ut=C("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),pr=C("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),vr=C("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]),ht=C("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["polyline",{points:"22 4 12 14.01 9 11.01",key:"6xbx8j"}]]),yr=C("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]),br=C("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]),mt=C("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),oe=C("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),jr=C("MicOff",[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M18.89 13.23A7.12 7.12 0 0 0 19 12v-2",key:"80xlxr"}],["path",{d:"M5 10v2a7 7 0 0 0 12 5",key:"p2k8kg"}],["path",{d:"M15 9.34V5a3 3 0 0 0-5.68-1.33",key:"1gzdoj"}],["path",{d:"M9 9v3a3 3 0 0 0 5.12 2.12",key:"r2i35w"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]),he=C("Mic",[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]),wr=C("Monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]),ft=C("QrCode",[["rect",{width:"5",height:"5",x:"3",y:"3",rx:"1",key:"1tu5fj"}],["rect",{width:"5",height:"5",x:"16",y:"3",rx:"1",key:"1v8r4q"}],["rect",{width:"5",height:"5",x:"3",y:"16",rx:"1",key:"1x03jg"}],["path",{d:"M21 16h-3a2 2 0 0 0-2 2v3",key:"177gqh"}],["path",{d:"M21 21v.01",key:"ents32"}],["path",{d:"M12 7v3a2 2 0 0 1-2 2H7",key:"8crl2c"}],["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M12 3h.01",key:"n36tog"}],["path",{d:"M12 16v.01",key:"133mhm"}],["path",{d:"M16 12h1",key:"1slzba"}],["path",{d:"M21 12v.01",key:"1lwtk9"}],["path",{d:"M12 21v-1",key:"1880an"}]]),we=C("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),Nr=C("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]),me=C("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),le=C("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]),gt=C("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),Sr=C("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]),Cr=C("Volume2",[["polygon",{points:"11 5 6 9 2 9 2 15 6 15 11 19 11 5",key:"16drj5"}],["path",{d:"M15.54 8.46a5 5 0 0 1 0 7.07",key:"ltjumu"}],["path",{d:"M19.07 4.93a10 10 0 0 1 0 14.14",key:"1kegas"}]]),kr=C("WifiOff",[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M8.5 16.5a5 5 0 0 1 7 0",key:"sej527"}],["path",{d:"M2 8.82a15 15 0 0 1 4.17-2.65",key:"11utq1"}],["path",{d:"M10.66 5c4.01-.36 8.14.9 11.34 3.76",key:"hxefdu"}],["path",{d:"M16.85 11.25a10 10 0 0 1 2.22 1.68",key:"q734kn"}],["path",{d:"M5 13a10 10 0 0 1 5.24-2.76",key:"piq4yl"}],["line",{x1:"12",x2:"12.01",y1:"20",y2:"20",key:"of4bc4"}]]),Er=C("Wifi",[["path",{d:"M5 13a10 10 0 0 1 14 0",key:"6v8j51"}],["path",{d:"M8.5 16.5a5 5 0 0 1 7 0",key:"sej527"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["line",{x1:"12",x2:"12.01",y1:"20",y2:"20",key:"of4bc4"}]]),Rr=C("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),Pr=C("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Dr=C("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]),Ir=()=>{const e=te(),{deviceName:n,getPairingStatus:t}=G(),{isConnected:s}=W(),i=t(),a=()=>{switch(e.pathname){case"/":return"SmartInput";case"/pairing":return"设备配对";case"/voice":return"语音输入";case"/settings":return"设置";default:return"SmartInput"}},o=()=>s?i==="paired"?"text-green-500":i==="pairing"?"text-yellow-500":"text-gray-500":"text-red-500",d=()=>s?i==="paired"?"已配对":i==="pairing"?"配对中":"未配对":"未连接";return r.jsx("header",{className:"bg-white shadow-sm border-b border-gray-200 safe-area-top",children:r.jsxs("div",{className:"px-4 py-3",children:[r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsxs("div",{className:"flex items-center space-x-3",children:[r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsx(le,{className:"w-6 h-6 text-blue-600"}),r.jsx("h1",{className:"text-lg font-semibold text-gray-900",children:a()})]}),r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx("div",{className:`w-2 h-2 rounded-full ${s?"bg-green-500":"bg-red-500"}`}),r.jsx("span",{className:`text-xs font-medium ${o()}`,children:d()})]})]}),r.jsxs("nav",{className:"flex items-center space-x-1",children:[r.jsx(se,{to:"/",className:`p-2 rounded-lg transition-colors ${e.pathname==="/"?"bg-blue-100 text-blue-600":"text-gray-600 hover:bg-gray-100"}`,title:"首页",children:r.jsx(br,{className:"w-5 h-5"})}),r.jsx(se,{to:"/voice",className:`p-2 rounded-lg transition-colors ${e.pathname==="/voice"?"bg-blue-100 text-blue-600":"text-gray-600 hover:bg-gray-100"}`,title:"语音输入",children:r.jsx(he,{className:"w-5 h-5"})}),r.jsx(se,{to:"/settings",className:`p-2 rounded-lg transition-colors ${e.pathname==="/settings"?"bg-blue-100 text-blue-600":"text-gray-600 hover:bg-gray-100"}`,title:"设置",children:r.jsx(me,{className:"w-5 h-5"})})]})]}),n&&r.jsxs("div",{className:"mt-2 text-xs text-gray-500",children:["设备: ",n]})]})})},Tr=()=>{const{deviceId:e,deviceName:n,isRegistered:t,pairedDevices:s,getPairingStatus:i,initializeDevice:a,registerDevice:o}=G(),{socket:d,isConnected:l}=W(),c=i();f.useEffect(()=>{(()=>T(O,null,function*(){try{e||(yield a()),d&&l&&!t&&(yield o(d))}catch(v){console.error("设备初始化失败:",v)}}))()},[d,l,e,t,a,o]);const u=(()=>{const g=[];return c==="paired"&&g.push({id:"voice",title:"开始语音输入",description:"按住录音，松开发送",icon:r.jsx(he,{className:"w-6 h-6"}),link:"/voice",color:"bg-blue-600 hover:bg-blue-700",primary:!0}),c==="unpaired"&&g.push({id:"pairing",title:"配对设备",description:"扫描二维码连接设备",icon:r.jsx(ft,{className:"w-6 h-6"}),link:"/pairing",color:"bg-green-600 hover:bg-green-700",primary:!0}),g.push({id:"settings",title:"设置",description:"调整应用设置",icon:r.jsx(me,{className:"w-6 h-6"}),link:"/settings",color:"bg-gray-600 hover:bg-gray-700"}),g})();return r.jsx("div",{className:"min-h-screen bg-gray-50 p-4",children:r.jsxs("div",{className:"max-w-md mx-auto space-y-6",children:[r.jsx("div",{className:"card",children:r.jsxs("div",{className:"text-center",children:[r.jsx("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:r.jsx(le,{className:"w-8 h-8 text-blue-600"})}),r.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-2",children:"欢迎使用 SmartInput"}),r.jsx("p",{className:"text-gray-600 text-sm",children:"通过语音输入，让文字输入更高效"})]})}),r.jsxs("div",{className:"card",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"设备状态"}),r.jsxs("div",{className:"space-y-3",children:[r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsxs("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:`w-3 h-3 rounded-full ${l?"bg-green-500":"bg-red-500"}`}),r.jsx("span",{className:"text-sm text-gray-700",children:"服务器连接"})]}),r.jsx("span",{className:`text-sm font-medium ${l?"text-green-600":"text-red-600"}`,children:l?"已连接":"未连接"})]}),r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsxs("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:`w-3 h-3 rounded-full ${t?"bg-green-500":"bg-yellow-500"}`}),r.jsx("span",{className:"text-sm text-gray-700",children:"设备注册"})]}),r.jsx("span",{className:`text-sm font-medium ${t?"text-green-600":"text-yellow-600"}`,children:t?"已注册":"未注册"})]}),r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsxs("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:`w-3 h-3 rounded-full ${c==="paired"?"bg-green-500":c==="pairing"?"bg-yellow-500":"bg-gray-400"}`}),r.jsx("span",{className:"text-sm text-gray-700",children:"设备配对"})]}),r.jsx("span",{className:`text-sm font-medium ${c==="paired"?"text-green-600":c==="pairing"?"text-yellow-600":"text-gray-600"}`,children:c==="paired"?`已配对 (${s.length})`:c==="pairing"?"配对中":"未配对"})]})]})]}),s.length>0&&r.jsxs("div",{className:"card",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"已配对设备"}),r.jsx("div",{className:"space-y-2",children:s.map(g=>r.jsxs("div",{className:"flex items-center space-x-3 p-2 bg-gray-50 rounded-lg",children:[r.jsx("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:g.type==="browser"?r.jsx(wr,{className:"w-4 h-4 text-blue-600"}):r.jsx(le,{className:"w-4 h-4 text-blue-600"})}),r.jsxs("div",{className:"flex-1",children:[r.jsx("div",{className:"text-sm font-medium text-gray-900",children:g.name}),r.jsx("div",{className:"text-xs text-gray-500",children:g.type==="browser"?"浏览器":"桌面应用"})]}),r.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"})]},g.id))})]}),r.jsx("div",{className:"space-y-3",children:u.map(g=>r.jsx(se,{to:g.link,className:`block w-full p-4 rounded-xl text-white transition-colors ${g.color} ${g.primary?"shadow-lg":""}`,children:r.jsxs("div",{className:"flex items-center space-x-4",children:[r.jsx("div",{className:"flex-shrink-0",children:g.icon}),r.jsxs("div",{className:"flex-1",children:[r.jsx("h3",{className:"text-lg font-semibold",children:g.title}),r.jsx("p",{className:"text-sm opacity-90",children:g.description})]}),r.jsx("div",{className:"flex-shrink-0",children:r.jsx(Dr,{className:"w-5 h-5"})})]})},g.id))}),r.jsx("div",{className:"card bg-blue-50 border-blue-200",children:r.jsxs("div",{className:"text-center",children:[r.jsx("h3",{className:"text-lg font-semibold text-blue-900 mb-2",children:"使用提示"}),r.jsxs("div",{className:"text-sm text-blue-700 space-y-1",children:[r.jsx("p",{children:"1. 首先配对您的电脑或浏览器"}),r.jsx("p",{children:"2. 然后就可以开始语音输入了"}),r.jsx("p",{children:"3. 语音会自动转换为文字并发送到配对设备"})]})]})})]})})},Mr=()=>{const[e,n]=f.useState(!1),[t,s]=f.useState(null),[i,a]=f.useState(""),[o,d]=f.useState(!1),l=f.useRef(null),c=f.useRef(null),{isPairing:h,pairingError:u,pairingCode:g,startPairing:v,onPairingSuccess:y,onPairingError:x}=G(),{socket:m,emit:b,on:p,off:j}=W();f.useEffect(()=>{const k=N=>{d(!1),N.success?y(N):x(N.error||"配对失败")};return m&&p("device:pair:result",k),()=>{m&&j("device:pair:result",k)}},[m,p,j,y,x]);const R=()=>T(O,null,function*(){try{if(s(null),!l.current)throw new Error("视频元素未准备好");if(!(yield Me.hasCamera()))throw new Error("未检测到摄像头");c.current=new Me(l.current,N=>{console.log("扫描到二维码:",N.data),q(N.data)},{onDecodeError:N=>{console.debug("QR解码错误:",N)},highlightScanRegion:!0,highlightCodeOutline:!0,preferredCamera:"environment"}),yield c.current.start(),n(!0)}catch(k){console.error("启动扫描器失败:",k),s(k.message)}}),D=()=>{c.current&&(c.current.stop(),c.current.destroy(),c.current=null),n(!1)},q=k=>T(O,null,function*(){try{D();const N=JSON.parse(k);if(N.type!=="smartinput_pairing")throw new Error("无效的配对二维码");yield M(N.code)}catch(N){console.error("处理二维码失败:",N),s("无效的二维码格式"),setTimeout(()=>{R()},2e3)}}),M=k=>T(O,null,function*(){if(!m){x("服务器连接不可用");return}try{d(!0),yield v(k),b("device:pair",{pairingCode:k})}catch(N){d(!1),x(N.message)}}),L=()=>T(O,null,function*(){if(!i.trim()){s("请输入配对码");return}yield M(i.trim().toUpperCase())});return f.useEffect(()=>()=>{D()},[]),r.jsx("div",{className:"min-h-screen bg-gray-50 p-4",children:r.jsxs("div",{className:"max-w-md mx-auto space-y-6",children:[r.jsxs("div",{className:"text-center",children:[r.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"设备配对"}),r.jsx("p",{className:"text-gray-600",children:"扫描电脑或浏览器上的二维码进行配对"})]}),r.jsx("div",{className:"card",children:r.jsx("div",{className:"text-center",children:e?r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{className:"relative",children:[r.jsx("video",{ref:l,className:"w-full h-64 bg-black rounded-lg object-cover",playsInline:!0,muted:!0}),o&&r.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg",children:r.jsxs("div",{className:"text-white text-center",children:[r.jsx(oe,{className:"w-8 h-8 animate-spin mx-auto mb-2"}),r.jsx("p",{children:"处理中..."})]})})]}),r.jsx("button",{onClick:D,className:"btn btn-secondary w-full",children:"停止扫描"})]}):r.jsxs("div",{className:"space-y-4",children:[r.jsx("div",{className:"w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto",children:r.jsx(ft,{className:"w-12 h-12 text-blue-600"})}),r.jsxs("button",{onClick:R,disabled:o,className:"btn btn-primary btn-lg w-full",children:[r.jsx(vr,{className:"w-5 h-5 mr-2"}),"开始扫描二维码"]})]})})}),r.jsxs("div",{className:"card",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"手动输入配对码"}),r.jsxs("div",{className:"space-y-3",children:[r.jsx("input",{type:"text",value:i,onChange:k=>a(k.target.value.toUpperCase()),placeholder:"输入8位配对码",className:"input",maxLength:8,disabled:o}),r.jsx("button",{onClick:L,disabled:!i.trim()||o,className:"btn btn-primary w-full",children:o?r.jsxs(r.Fragment,{children:[r.jsx(oe,{className:"w-4 h-4 animate-spin mr-2"}),"配对中..."]}):"开始配对"})]})]}),(t||u)&&r.jsxs("div",{className:"card bg-red-50 border-red-200",children:[r.jsxs("div",{className:"flex items-center space-x-3",children:[r.jsx(Rr,{className:"w-5 h-5 text-red-600 flex-shrink-0"}),r.jsxs("div",{children:[r.jsx("h4",{className:"text-sm font-medium text-red-800",children:"配对失败"}),r.jsx("p",{className:"text-sm text-red-600 mt-1",children:t||u})]})]}),r.jsxs("button",{onClick:()=>{s(null),x(null)},className:"mt-3 text-sm text-red-600 hover:text-red-800",children:[r.jsx(we,{className:"w-4 h-4 inline mr-1"}),"重试"]})]}),h&&!u&&r.jsx("div",{className:"card bg-green-50 border-green-200",children:r.jsxs("div",{className:"flex items-center space-x-3",children:[r.jsx(ht,{className:"w-5 h-5 text-green-600"}),r.jsxs("div",{children:[r.jsx("h4",{className:"text-sm font-medium text-green-800",children:"配对成功"}),r.jsx("p",{className:"text-sm text-green-600 mt-1",children:"设备已成功配对，可以开始使用语音输入功能"})]})]})}),r.jsxs("div",{className:"card bg-blue-50 border-blue-200",children:[r.jsx("h3",{className:"text-lg font-semibold text-blue-900 mb-3",children:"配对步骤"}),r.jsxs("div",{className:"space-y-2 text-sm text-blue-700",children:[r.jsxs("div",{className:"flex items-start space-x-2",children:[r.jsx("span",{className:"w-5 h-5 bg-blue-200 rounded-full flex items-center justify-center text-xs font-bold text-blue-800 flex-shrink-0 mt-0.5",children:"1"}),r.jsx("p",{children:"在电脑浏览器中安装SmartInput扩展"})]}),r.jsxs("div",{className:"flex items-start space-x-2",children:[r.jsx("span",{className:"w-5 h-5 bg-blue-200 rounded-full flex items-center justify-center text-xs font-bold text-blue-800 flex-shrink-0 mt-0.5",children:"2"}),r.jsx("p",{children:"点击扩展图标，生成配对二维码"})]}),r.jsxs("div",{className:"flex items-start space-x-2",children:[r.jsx("span",{className:"w-5 h-5 bg-blue-200 rounded-full flex items-center justify-center text-xs font-bold text-blue-800 flex-shrink-0 mt-0.5",children:"3"}),r.jsx("p",{children:"使用手机扫描二维码完成配对"})]})]})]})]})})},Ee=ke()(dt((e,n)=>({isRecording:!1,isProcessing:!1,recordingDuration:0,recordingStartTime:null,recognitionText:"",finalText:"",confidence:0,language:"zh-CN",audioBlob:null,audioUrl:null,waveformData:[],error:null,lastError:null,voiceHistory:[],maxHistoryItems:50,settings:{autoSend:!0,continuous:!1,interimResults:!0,maxAlternatives:1,noiseReduction:!0,echoCancellation:!0,autoGainControl:!0},startRecording:()=>{e({isRecording:!0,isProcessing:!1,recordingStartTime:Date.now(),recordingDuration:0,recognitionText:"",finalText:"",error:null,audioBlob:null,audioUrl:null,waveformData:[]})},stopRecording:()=>{const{recordingStartTime:t}=n(),s=t?Date.now()-t:0;e({isRecording:!1,isProcessing:!0,recordingDuration:s})},updateRecordingDuration:()=>{const{recordingStartTime:t,isRecording:s}=n();if(s&&t){const i=Date.now()-t;e({recordingDuration:i})}},setAudioData:(t,s)=>{e({audioBlob:t,audioUrl:s,isProcessing:!1})},updateWaveform:t=>{e({waveformData:t})},setRecognitionText:(t,s=!1,i=0)=>{s?(e({finalText:t,recognitionText:"",confidence:i,isProcessing:!1}),n().addToHistory(t,i)):e({recognitionText:t,confidence:i})},addToHistory:(t,s)=>{const{voiceHistory:i,maxHistoryItems:a}=n(),d=[{id:Date.now(),text:t,confidence:s,timestamp:new Date().toISOString(),language:n().language},...i].slice(0,a);e({voiceHistory:d})},clearHistory:()=>{e({voiceHistory:[]})},removeHistoryItem:t=>{const{voiceHistory:s}=n();e({voiceHistory:s.filter(i=>i.id!==t)})},setLanguage:t=>{e({language:t})},setError:t=>{e({error:t,lastError:t,isRecording:!1,isProcessing:!1})},clearError:()=>{e({error:null})},reset:()=>{e({isRecording:!1,isProcessing:!1,recordingDuration:0,recordingStartTime:null,recognitionText:"",finalText:"",confidence:0,audioBlob:null,audioUrl:null,waveformData:[],error:null})},updateSettings:t=>{e({settings:E(E({},n().settings),t)})},getRecordingStatus:()=>{const{isRecording:t,isProcessing:s,error:i}=n();return i?"error":t?"recording":s?"processing":"idle"},getFormattedDuration:()=>{const{recordingDuration:t}=n(),s=Math.floor(t/1e3),i=Math.floor(s/60),a=s%60;return i>0?`${i}:${a.toString().padStart(2,"0")}`:`${a}s`},getCurrentText:()=>{const{finalText:t,recognitionText:s}=n();return t||s},hasAudioData:()=>{const{audioBlob:t}=n();return!!t},getStats:()=>{const{voiceHistory:t,settings:s,language:i}=n();return{totalRecordings:t.length,averageConfidence:t.length>0?t.reduce((a,o)=>a+o.confidence,0)/t.length:0,currentLanguage:i,settings:s}}})));setInterval(()=>{const{updateRecordingDuration:e,isRecording:n}=Ee.getState();n&&e()},100);class Lr{constructor(){this.recognition=null,this.isListening=!1,this.callbacks={onResult:void 0,onError:void 0,onStart:void 0,onEnd:void 0},this.defaultConfig={language:"zh-CN",continuous:!1,interimResults:!0,maxAlternatives:1},this.isSupported=this.checkSupport()}checkSupport(){return"webkitSpeechRecognition"in window||"SpeechRecognition"in window}initialize(n={}){if(!this.isSupported)throw new Error("当前浏览器不支持语音识别");const t=window.SpeechRecognition||window.webkitSpeechRecognition;if(!t)throw new Error("语音识别API不可用");this.recognition=new t;const s=E(E({},this.defaultConfig),n);return this.recognition.lang=s.language||"zh-CN",this.recognition.continuous=s.continuous||!1,this.recognition.interimResults=s.interimResults||!0,this.recognition.maxAlternatives=s.maxAlternatives||1,this.setupEventListeners(),this.recognition}setupEventListeners(){this.recognition&&(this.recognition.onstart=()=>{var n,t;this.isListening=!0,console.log("语音识别开始"),(t=(n=this.callbacks).onStart)==null||t.call(n)},this.recognition.onresult=n=>{var a,o,d,l;let t="",s="";for(let c=n.resultIndex;c<n.results.length;c++){const h=n.results[c],u=h[0].transcript;h.isFinal?s+=u:t+=u}const i={text:s||t,confidence:((o=(a=n.results[n.results.length-1])==null?void 0:a[0])==null?void 0:o.confidence)||0,isFinal:s.length>0,timestamp:Date.now()};(l=(d=this.callbacks).onResult)==null||l.call(d,i)},this.recognition.onerror=n=>{var s,i;console.error("语音识别错误:",n.error),this.isListening=!1;let t="语音识别出错";switch(n.error){case"no-speech":t="没有检测到语音";break;case"audio-capture":t="无法捕获音频";break;case"not-allowed":t="麦克风权限被拒绝";break;case"network":t="网络错误";break;case"service-not-allowed":t="语音识别服务不可用";break;default:t=`语音识别错误: ${n.error}`}(i=(s=this.callbacks).onError)==null||i.call(s,new Error(t))},this.recognition.onend=()=>{var n,t;this.isListening=!1,console.log("语音识别结束"),(t=(n=this.callbacks).onEnd)==null||t.call(n)},this.recognition.onaudiostart=()=>{console.log("音频捕获开始")},this.recognition.onaudioend=()=>{console.log("音频捕获结束")},this.recognition.onspeechstart=()=>{console.log("检测到语音")},this.recognition.onspeechend=()=>{console.log("语音结束")},this.recognition.onsoundstart=()=>{console.log("检测到声音")},this.recognition.onsoundend=()=>{console.log("声音结束")})}start(n={}){var t,s;if(!this.recognition)throw new Error("语音识别未初始化");if(this.isListening){console.warn("语音识别已在进行中");return}this.callbacks=E(E({},this.callbacks),n);try{this.recognition.start()}catch(i){console.error("启动语音识别失败:",i),(s=(t=this.callbacks).onError)==null||s.call(t,i)}}stop(){this.recognition&&this.isListening&&this.recognition.stop()}abort(){this.recognition&&this.isListening&&this.recognition.abort()}updateConfig(n){this.recognition&&(n.language&&(this.recognition.lang=n.language),n.continuous!==void 0&&(this.recognition.continuous=n.continuous),n.interimResults!==void 0&&(this.recognition.interimResults=n.interimResults),n.maxAlternatives!==void 0&&(this.recognition.maxAlternatives=n.maxAlternatives))}getSupportedLanguages(){return[{code:"zh-CN",name:"中文(简体)"},{code:"zh-TW",name:"中文(繁体)"},{code:"en-US",name:"English (US)"},{code:"en-GB",name:"English (UK)"},{code:"ja-JP",name:"日本語"},{code:"ko-KR",name:"한국어"},{code:"fr-FR",name:"Français"},{code:"de-DE",name:"Deutsch"},{code:"es-ES",name:"Español"},{code:"it-IT",name:"Italiano"},{code:"pt-BR",name:"Português (Brasil)"},{code:"ru-RU",name:"Русский"},{code:"ar-SA",name:"العربية"},{code:"hi-IN",name:"हिन्दी"},{code:"th-TH",name:"ไทย"}]}checkMicrophonePermission(){return T(this,null,function*(){try{return(yield navigator.mediaDevices.getUserMedia({audio:!0})).getTracks().forEach(t=>t.stop()),!0}catch(n){return console.error("麦克风权限检查失败:",n),!1}})}requestMicrophonePermission(){return T(this,null,function*(){try{return(yield navigator.mediaDevices.getUserMedia({audio:{echoCancellation:!0,noiseSuppression:!0,autoGainControl:!0}})).getTracks().forEach(t=>t.stop()),!0}catch(n){throw console.error("请求麦克风权限失败:",n),new Error("无法获取麦克风权限")}})}getStatus(){return{isSupported:this.isSupported,isListening:this.isListening,hasRecognition:!!this.recognition}}destroy(){this.recognition&&(this.stop(),this.recognition=null),this.callbacks={onResult:void 0,onError:void 0,onStart:void 0,onEnd:void 0}}}const B=new Lr,Or=()=>{const[e,n]=f.useState(!1);f.useState(null);const{language:t,settings:s,voiceHistory:i,setLanguage:a,updateSettings:o,clearHistory:d,getStats:l}=Ee(),{deviceName:c,capabilities:h,updateDeviceName:u,resetDevice:g,getDeviceStats:v}=G(),{serverUrl:y,isConnected:x,connectionError:m,updateServerUrl:b,forceReconnect:p}=W(),j=l(),R=v(),D=B.getSupportedLanguages(),q=w=>{a(w),B.updateConfig({language:w})},M=(w,I)=>{const $=fe(E({},s),{[w]:I});o($),["continuous","interimResults","maxAlternatives"].includes(w)&&B.updateConfig({[w]:I})},L=()=>{const w={version:"1.0",timestamp:new Date().toISOString(),device:R,settings:{language:t,voiceSettings:s},history:i,stats:j},I=new Blob([JSON.stringify(w,null,2)],{type:"application/json"}),$=URL.createObjectURL(I),_=document.createElement("a");_.href=$,_.download=`smartinput-data-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(_),_.click(),document.body.removeChild(_),URL.revokeObjectURL($)},k=w=>{const I=w.target.files[0];if(!I)return;const $=new FileReader;$.onload=_=>{var ne,A;try{const U=JSON.parse(_.target.result);(ne=U.settings)!=null&&ne.language&&q(U.settings.language),(A=U.settings)!=null&&A.voiceSettings&&o(U.settings.voiceSettings),alert("数据导入成功")}catch(U){alert("数据导入失败：文件格式错误")}},$.readAsText(I)},N=()=>{confirm("确定要重置所有设置吗？这将清除所有数据和配对信息。")&&(d(),g(),o({autoSend:!0,continuous:!1,interimResults:!0,maxAlternatives:1,noiseReduction:!0,echoCancellation:!0,autoGainControl:!0}),a("zh-CN"),alert("设置已重置"))};return r.jsx("div",{className:"min-h-screen bg-gray-50 p-4",children:r.jsxs("div",{className:"max-w-md mx-auto space-y-6",children:[r.jsxs("div",{className:"card",children:[r.jsxs("div",{className:"flex items-center space-x-2 mb-4",children:[r.jsx(he,{className:"w-5 h-5 text-blue-600"}),r.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"语音设置"})]}),r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"识别语言"}),r.jsx("select",{value:t,onChange:w=>q(w.target.value),className:"input",children:D.map(w=>r.jsx("option",{value:w.code,children:w.name},w.code))})]}),r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsxs("div",{children:[r.jsx("label",{className:"text-sm font-medium text-gray-700",children:"自动发送"}),r.jsx("p",{className:"text-xs text-gray-500",children:"识别完成后自动发送文字"})]}),r.jsxs("label",{className:"switch",children:[r.jsx("input",{type:"checkbox",checked:s.autoSend,onChange:w=>M("autoSend",w.target.checked)}),r.jsx("span",{className:"slider"})]})]}),r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsxs("div",{children:[r.jsx("label",{className:"text-sm font-medium text-gray-700",children:"连续识别"}),r.jsx("p",{className:"text-xs text-gray-500",children:"持续监听语音输入"})]}),r.jsxs("label",{className:"switch",children:[r.jsx("input",{type:"checkbox",checked:s.continuous,onChange:w=>M("continuous",w.target.checked)}),r.jsx("span",{className:"slider"})]})]}),r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsxs("div",{children:[r.jsx("label",{className:"text-sm font-medium text-gray-700",children:"实时结果"}),r.jsx("p",{className:"text-xs text-gray-500",children:"显示识别过程中的临时结果"})]}),r.jsxs("label",{className:"switch",children:[r.jsx("input",{type:"checkbox",checked:s.interimResults,onChange:w=>M("interimResults",w.target.checked)}),r.jsx("span",{className:"slider"})]})]})]})]}),r.jsxs("div",{className:"card",children:[r.jsxs("div",{className:"flex items-center space-x-2 mb-4",children:[r.jsx(le,{className:"w-5 h-5 text-green-600"}),r.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"设备设置"})]}),r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"设备名称"}),r.jsx("input",{type:"text",value:c,onChange:w=>u(w.target.value),className:"input",placeholder:"输入设备名称"})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"服务器地址"}),r.jsxs("div",{className:"flex space-x-2",children:[r.jsx("input",{type:"url",value:y,onChange:w=>b(w.target.value),className:"input flex-1",placeholder:"ws://localhost:3001"}),r.jsx("button",{onClick:p,className:"btn btn-secondary",title:"重新连接",children:r.jsx(we,{className:"w-4 h-4"})})]}),r.jsxs("div",{className:"mt-1 flex items-center space-x-2",children:[r.jsx("div",{className:`w-2 h-2 rounded-full ${x?"bg-green-500":"bg-red-500"}`}),r.jsx("span",{className:"text-xs text-gray-500",children:x?"已连接":m||"未连接"})]})]})]})]}),r.jsxs("div",{className:"card",children:[r.jsxs("div",{className:"flex items-center space-x-2 mb-4",children:[r.jsx(mt,{className:"w-5 h-5 text-purple-600"}),r.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"使用统计"})]}),r.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[r.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[r.jsx("div",{className:"text-2xl font-bold text-blue-600",children:j.totalRecordings}),r.jsx("div",{className:"text-xs text-gray-500",children:"总录音次数"})]}),r.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[r.jsxs("div",{className:"text-2xl font-bold text-green-600",children:[Math.round(j.averageConfidence*100),"%"]}),r.jsx("div",{className:"text-xs text-gray-500",children:"平均准确率"})]}),r.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[r.jsx("div",{className:"text-2xl font-bold text-purple-600",children:R.pairedDevicesCount}),r.jsx("div",{className:"text-xs text-gray-500",children:"配对设备"})]}),r.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[r.jsx("div",{className:"text-2xl font-bold text-orange-600",children:R.supportedFeatures.length}),r.jsx("div",{className:"text-xs text-gray-500",children:"支持功能"})]})]})]}),r.jsxs("div",{className:"card",children:[r.jsxs("button",{onClick:()=>n(!e),className:"flex items-center justify-between w-full text-left",children:[r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsx(me,{className:"w-5 h-5 text-gray-600"}),r.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"高级设置"})]}),r.jsx("div",{className:`transform transition-transform ${e?"rotate-180":""}`,children:"▼"})]}),e&&r.jsxs("div",{className:"mt-4 space-y-4 border-t pt-4",children:[r.jsxs("div",{className:"space-y-3",children:[r.jsx("h4",{className:"text-sm font-semibold text-gray-700",children:"音频增强"}),r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsx("span",{className:"text-sm text-gray-600",children:"噪音抑制"}),r.jsxs("label",{className:"switch",children:[r.jsx("input",{type:"checkbox",checked:s.noiseReduction,onChange:w=>M("noiseReduction",w.target.checked)}),r.jsx("span",{className:"slider"})]})]}),r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsx("span",{className:"text-sm text-gray-600",children:"回声消除"}),r.jsxs("label",{className:"switch",children:[r.jsx("input",{type:"checkbox",checked:s.echoCancellation,onChange:w=>M("echoCancellation",w.target.checked)}),r.jsx("span",{className:"slider"})]})]}),r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsx("span",{className:"text-sm text-gray-600",children:"自动增益"}),r.jsxs("label",{className:"switch",children:[r.jsx("input",{type:"checkbox",checked:s.autoGainControl,onChange:w=>M("autoGainControl",w.target.checked)}),r.jsx("span",{className:"slider"})]})]})]}),r.jsxs("div",{className:"space-y-3",children:[r.jsx("h4",{className:"text-sm font-semibold text-gray-700",children:"数据管理"}),r.jsxs("div",{className:"flex space-x-2",children:[r.jsxs("button",{onClick:L,className:"btn btn-secondary flex-1",children:[r.jsx(yr,{className:"w-4 h-4 mr-2"}),"导出数据"]}),r.jsxs("label",{className:"btn btn-secondary flex-1 cursor-pointer",children:[r.jsx(Sr,{className:"w-4 h-4 mr-2"}),"导入数据",r.jsx("input",{type:"file",accept:".json",onChange:k,className:"hidden"})]})]}),r.jsxs("button",{onClick:()=>d(),className:"btn btn-danger w-full",children:[r.jsx(gt,{className:"w-4 h-4 mr-2"}),"清除历史记录"]}),r.jsxs("button",{onClick:N,className:"btn btn-danger w-full",children:[r.jsx(we,{className:"w-4 h-4 mr-2"}),"重置所有设置"]})]})]})]}),r.jsxs("div",{className:"card",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"设备能力"}),r.jsx("div",{className:"grid grid-cols-2 gap-2 text-xs",children:Object.entries(h).map(([w,I])=>r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsx("div",{className:`w-2 h-2 rounded-full ${I?"bg-green-500":"bg-gray-300"}`}),r.jsx("span",{className:I?"text-gray-700":"text-gray-400",children:w})]},w))})]})]})})},$r=()=>{const[e,n]=f.useState(!1),[t,s]=f.useState(null),{isRecording:i,isProcessing:a,recognitionText:o,finalText:d,confidence:l,language:c,error:h,voiceHistory:u,settings:g,startRecording:v,stopRecording:y,setRecognitionText:x,setError:m,clearError:b,reset:p,getRecordingStatus:j,getFormattedDuration:R,getCurrentText:D}=Ee(),{pairedDevices:q,getPairingStatus:M}=G(),{emit:L,isConnected:k}=W(),N=M();f.useEffect(()=>{(()=>T(O,null,function*(){try{if(!B.isSupported)throw new Error("当前浏览器不支持语音识别");yield B.requestMicrophonePermission(),B.initialize({language:c,continuous:g.continuous,interimResults:g.interimResults,maxAlternatives:g.maxAlternatives}),n(!0),s(null)}catch(Re){console.error("语音识别初始化失败:",Re),s(Re.message)}}))()},[c,g]);const w=f.useCallback(()=>T(O,null,function*(){if(!(!e||i))try{b(),v(),pe(50),k&&N==="paired"&&L("voice:start",{timestamp:Date.now(),language:c}),B.start({onResult:S=>{x(S.finalTranscript||S.interimTranscript,!!S.finalTranscript,S.confidence)},onError:S=>{m(S.message),y()},onEnd:()=>{i&&I()}})}catch(S){m(S.message),y()}}),[e,i,b,v,k,N,L,c,x,m,y]),I=f.useCallback(()=>{if(i&&(B.stop(),y(),pe([50,50,50]),k&&N==="paired")){const S=D();S?(L("voice:end",{text:S,confidence:l,timestamp:Date.now(),language:c}),g.autoSend&&setTimeout(()=>$(S),500)):L("voice:end",{timestamp:Date.now()})}},[i,y,k,N,L,D,l,c,g.autoSend]),$=f.useCallback(S=>{!S||!k||N!=="paired"||(L("text:input",{text:S,timestamp:Date.now(),language:c}),pe(100),p())},[k,N,L,c,p]),_=()=>{p()},ne=()=>{p(),setTimeout(()=>{w()},100)},A=j(),U=D();return N!=="paired"?r.jsx("div",{className:"min-h-screen bg-gray-50 p-4 flex items-center justify-center",children:r.jsxs("div",{className:"max-w-md mx-auto text-center",children:[r.jsx("div",{className:"w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4",children:r.jsx(me,{className:"w-8 h-8 text-yellow-600"})}),r.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-2",children:"需要配对设备"}),r.jsx("p",{className:"text-gray-600 mb-4",children:"请先配对您的电脑或浏览器，然后才能使用语音输入功能"}),r.jsx("button",{onClick:()=>window.history.back(),className:"btn btn-primary",children:"返回配对"})]})}):r.jsx("div",{className:"min-h-screen bg-gray-50 p-4",children:r.jsxs("div",{className:"max-w-md mx-auto space-y-6",children:[t&&r.jsx("div",{className:"card bg-red-50 border-red-200",children:r.jsxs("div",{className:"text-center",children:[r.jsx("h3",{className:"text-lg font-semibold text-red-800 mb-2",children:"初始化失败"}),r.jsx("p",{className:"text-sm text-red-600 mb-4",children:t}),r.jsx("button",{onClick:()=>window.location.reload(),className:"btn btn-danger btn-sm",children:"重新加载"})]})}),r.jsx("div",{className:"card text-center",children:r.jsxs("div",{className:"space-y-4",children:[r.jsx("div",{className:"flex justify-center",children:r.jsx("button",{onMouseDown:w,onMouseUp:I,onTouchStart:w,onTouchEnd:I,disabled:!e||a,className:`recording-button ${A} touch-manipulation`,children:A==="recording"?r.jsx(jr,{className:"w-8 h-8"}):A==="processing"?r.jsx(Cr,{className:"w-8 h-8"}):r.jsx(he,{className:"w-8 h-8"})})}),r.jsxs("div",{className:"space-y-1",children:[r.jsxs("p",{className:"text-lg font-semibold text-gray-900",children:[A==="recording"&&"正在录音...",A==="processing"&&"处理中...",A==="idle"&&"按住录音",A==="error"&&"录音出错"]}),i&&r.jsxs("p",{className:"text-sm text-gray-600",children:["录音时长: ",R()]}),l>0&&r.jsxs("p",{className:"text-xs text-gray-500",children:["识别置信度: ",Math.round(l*100),"%"]})]}),r.jsx("p",{className:"text-sm text-gray-500",children:"按住录音按钮开始语音输入，松开停止录音"})]})}),(U||h)&&r.jsxs("div",{className:"card",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"识别结果"}),h?r.jsx("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 mb-4",children:r.jsx("p",{className:"text-red-600 text-sm",children:h})}):r.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-3 mb-4 min-h-[60px]",children:[o&&r.jsx("p",{className:"text-gray-500 text-sm mb-2",children:o}),d&&r.jsx("p",{className:"text-gray-900 font-medium",children:d}),!U&&r.jsx("p",{className:"text-gray-400 text-sm italic",children:"识别结果将显示在这里..."})]}),r.jsxs("div",{className:"flex space-x-2",children:[U&&!g.autoSend&&r.jsxs("button",{onClick:()=>$(U),className:"btn btn-primary flex-1",children:[r.jsx(Nr,{className:"w-4 h-4 mr-2"}),"发送"]}),r.jsx("button",{onClick:_,className:"btn btn-secondary",children:r.jsx(gt,{className:"w-4 h-4"})}),h&&r.jsx("button",{onClick:ne,className:"btn btn-primary",children:"重试"})]})]}),r.jsxs("div",{className:"card",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"配对设备"}),r.jsx("div",{className:"space-y-2",children:q.map(S=>r.jsxs("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded-lg",children:[r.jsxs("div",{children:[r.jsx("p",{className:"text-sm font-medium text-gray-900",children:S.name}),r.jsx("p",{className:"text-xs text-gray-500",children:S.type==="browser"?"浏览器":"桌面应用"})]}),r.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"})]},S.id))})]}),u.length>0&&r.jsxs("div",{className:"card",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"最近记录"}),r.jsx("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:u.slice(0,5).map(S=>r.jsxs("div",{className:"p-2 bg-gray-50 rounded-lg",children:[r.jsx("p",{className:"text-sm text-gray-900",children:S.text}),r.jsxs("div",{className:"flex justify-between items-center mt-1",children:[r.jsxs("span",{className:"text-xs text-gray-500",children:["置信度: ",Math.round(S.confidence*100),"%"]}),r.jsx("span",{className:"text-xs text-gray-500",children:new Date(S.timestamp).toLocaleTimeString()})]})]},S.id))})]})]})})},Ar=()=>{const{isConnected:e,isConnecting:n,connectionError:t,reconnectAttempts:s,getConnectionStatus:i,forceReconnect:a}=W(),o=i();if(o==="connected"&&!t)return null;const l=(()=>{switch(o){case"connecting":return{icon:r.jsx(oe,{className:"w-4 h-4 animate-spin"}),text:"正在连接服务器...",bgColor:"bg-yellow-50",textColor:"text-yellow-800",borderColor:"border-yellow-200"};case"reconnecting":return{icon:r.jsx(oe,{className:"w-4 h-4 animate-spin"}),text:`重连中... (${s}/5)`,bgColor:"bg-yellow-50",textColor:"text-yellow-800",borderColor:"border-yellow-200"};case"error":return{icon:r.jsx(ut,{className:"w-4 h-4"}),text:t||"连接出错",bgColor:"bg-red-50",textColor:"text-red-800",borderColor:"border-red-200"};case"disconnected":return{icon:r.jsx(kr,{className:"w-4 h-4"}),text:"服务器连接断开",bgColor:"bg-gray-50",textColor:"text-gray-800",borderColor:"border-gray-200"};default:return{icon:r.jsx(Er,{className:"w-4 h-4"}),text:"已连接",bgColor:"bg-green-50",textColor:"text-green-800",borderColor:"border-green-200"}}})(),c=o==="error"||o==="disconnected";return r.jsxs("div",{className:`${l.bgColor} ${l.borderColor} border-b px-4 py-2`,children:[r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsx("div",{className:l.textColor,children:l.icon}),r.jsx("span",{className:`text-sm font-medium ${l.textColor}`,children:l.text})]}),c&&r.jsx("button",{onClick:a,className:`text-xs px-3 py-1 rounded-full border transition-colors ${l.textColor==="text-red-800"?"border-red-300 text-red-700 hover:bg-red-100":"border-gray-300 text-gray-700 hover:bg-gray-100"}`,children:"重试连接"})]}),o==="error"&&r.jsx("div",{className:"mt-2 text-xs text-red-600",children:"请检查网络连接或稍后重试"})]})},Ur=({message:e,type:n="info",duration:t=3e3,onClose:s,position:i="top-center"})=>{const[a,o]=f.useState(!0),[d,l]=f.useState(!1);f.useEffect(()=>{if(t>0){const v=setTimeout(()=>{c()},t);return()=>clearTimeout(v)}},[t]);const c=()=>{l(!0),setTimeout(()=>{o(!1),s==null||s()},300)};if(!a)return null;const h=()=>{switch(n){case"success":return{icon:r.jsx(ht,{className:"w-5 h-5"}),bgColor:"bg-green-500",textColor:"text-white"};case"error":return{icon:r.jsx(ut,{className:"w-5 h-5"}),bgColor:"bg-red-500",textColor:"text-white"};case"warning":return{icon:r.jsx(pr,{className:"w-5 h-5"}),bgColor:"bg-yellow-500",textColor:"text-white"};default:return{icon:r.jsx(mt,{className:"w-5 h-5"}),bgColor:"bg-blue-500",textColor:"text-white"}}},u=()=>{switch(i){case"top-left":return"top-4 left-4";case"top-right":return"top-4 right-4";case"bottom-left":return"bottom-4 left-4";case"bottom-right":return"bottom-4 right-4";case"bottom-center":return"bottom-4 left-1/2 transform -translate-x-1/2";default:return"top-4 left-1/2 transform -translate-x-1/2"}},g=h();return r.jsx("div",{className:`fixed z-50 ${u()} ${d?"animate-fade-out":"animate-fade-in"}`,children:r.jsx("div",{className:`${g.bgColor} ${g.textColor} rounded-lg shadow-lg p-4 max-w-sm mx-auto`,children:r.jsxs("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"flex-shrink-0",children:g.icon}),r.jsx("div",{className:"flex-1",children:r.jsx("p",{className:"text-sm font-medium",children:e})}),r.jsx("button",{onClick:c,className:"flex-shrink-0 opacity-70 hover:opacity-100 transition-opacity",children:r.jsx(Pr,{className:"w-4 h-4"})})]})})})};class _r{constructor(){this.toasts=[],this.listeners=[]}show(n,t="info",s={}){const i={id:Date.now()+Math.random(),message:n,type:t,duration:s.duration||3e3,position:s.position||"top-center"};return this.toasts.push(i),this.notifyListeners(),i.id}hide(n){this.toasts=this.toasts.filter(t=>t.id!==n),this.notifyListeners()}clear(){this.toasts=[],this.notifyListeners()}success(n,t={}){return this.show(n,"success",t)}error(n,t={}){return this.show(n,"error",t)}warning(n,t={}){return this.show(n,"warning",t)}info(n,t={}){return this.show(n,"info",t)}subscribe(n){return this.listeners.push(n),()=>{this.listeners=this.listeners.filter(t=>t!==n)}}notifyListeners(){this.listeners.forEach(n=>n(this.toasts))}}const qe=new _r,Br=()=>{const[e,n]=f.useState([]);return f.useEffect(()=>qe.subscribe(n),[]),r.jsx(r.Fragment,{children:e.map(t=>r.jsx(Ur,{message:t.message,type:t.type,duration:t.duration,position:t.position,onClose:()=>qe.hide(t.id)},t.id))})},zr=()=>{const{isConnected:e,connect:n}=W(),{deviceId:t,initializeDevice:s}=G();return Ne.useEffect(()=>{(()=>T(O,null,function*(){try{t||(yield s()),e||n()}catch(o){console.error("应用初始化失败:",o)}}))();const a=()=>{document.hidden||e||n()};return document.addEventListener("visibilitychange",a),()=>{document.removeEventListener("visibilitychange",a)}},[e,n,t,s]),r.jsxs("div",{className:"min-h-screen bg-gray-50 flex flex-col",children:[r.jsx(Ir,{}),r.jsx(Ar,{}),r.jsx("main",{className:"flex-1 safe-area-bottom",children:r.jsxs(wn,{children:[r.jsx(Z,{path:"/",element:r.jsx(Tr,{})}),r.jsx(Z,{path:"/pairing",element:r.jsx(Mr,{})}),r.jsx(Z,{path:"/voice",element:r.jsx($r,{})}),r.jsx(Z,{path:"/settings",element:r.jsx(Or,{})})]})}),r.jsx(Br,{})]})};"serviceWorker"in navigator&&window.addEventListener("load",()=>{navigator.serviceWorker.register("/sw.js").then(e=>{console.log("SW registered: ",e)}).catch(e=>{console.log("SW registration failed: ",e)})});!("webkitSpeechRecognition"in window)&&!("SpeechRecognition"in window)&&console.warn("当前浏览器不支持语音识别功能");(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia)&&console.warn("当前浏览器不支持媒体录制功能");const xt=document.getElementById("root");if(!xt)throw new Error("Root element not found");ve.createRoot(xt).render(r.jsx(Ne.StrictMode,{children:r.jsx(Pn,{children:r.jsx(zr,{})})}))});export default Fr();
