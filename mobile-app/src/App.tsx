import React from 'react'
import { Routes, Route } from 'react-router-dom'
import { useSocketStore } from './stores/socketStore'
import { useDeviceStore } from './stores/deviceStore'
import Header from './components/Header'
import HomePage from './pages/HomePage'
import PairingPage from './pages/PairingPage'
import SettingsPage from './pages/SettingsPage'
import VoiceInputPage from './pages/VoiceInputPage'
import ConnectionStatus from './components/ConnectionStatus'
import { ToastContainer } from './components/Toast'

const App: React.FC = () => {
  const { isConnected, connect, disconnect } = useSocketStore()
  const { deviceId, isRegistered, initializeDevice } = useDeviceStore()

  React.useEffect(() => {
    // 初始化应用
    const initApp = async () => {
      try {
        // 初始化设备信息
        if (!deviceId) {
          await initializeDevice()
        }

        // 连接到服务器
        if (!isConnected) {
          connect()
        }
      } catch (error) {
        console.error('应用初始化失败:', error)
      }
    }

    initApp()

    // 页面可见性变化时处理连接
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // 页面隐藏时保持连接，但可以降低心跳频率
      } else {
        // 页面显示时确保连接正常
        if (!isConnected) {
          connect()
        }
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [isConnected, connect, deviceId, initializeDevice])

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <Header />
      <ConnectionStatus />
      
      <main className="flex-1 safe-area-bottom">
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/pairing" element={<PairingPage />} />
          <Route path="/voice" element={<VoiceInputPage />} />
          <Route path="/settings" element={<SettingsPage />} />
        </Routes>
      </main>

      <ToastContainer />
    </div>
  )
}

export default App
