import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { generateDeviceId, getDeviceInfo } from '../utils/device'
import type { DeviceInfo, DeviceCapabilities, RegisteredDevice } from '../types'

interface DeviceStoreState {
  // 设备信息
  deviceId: string | null;
  deviceName: string;
  deviceType: 'mobile' | 'browser' | 'desktop';
  isRegistered: boolean;
  registrationError: string | null;

  // 配对信息
  pairedDevices: RegisteredDevice[];
  currentSession: string | null;
  pairingCode: string;
  isPairing: boolean;
  pairingError: string | null;

  // 设备能力
  capabilities: {
    speechRecognition: boolean;
    mediaRecording: boolean;
    camera: boolean;
    vibration: boolean;
  };

  // 方法
  initializeDevice: () => Promise<{ deviceId: string; deviceInfo: any; capabilities: any }>;
  detectCapabilities: () => Promise<any>;
  registerDevice: (socket: any) => Promise<any>;
  startPairing: (pairingCode: string) => Promise<any>;
  onPairingSuccess: (sessionData: any) => void;
  onPairingError: (error: string) => void;
  disconnectPairing: () => void;
  updateDeviceName: (name: string) => void;
  getPairingStatus: () => 'pairing' | 'paired' | 'unpaired';
  resetDevice: () => void;
  getDeviceStats: () => any;
}

export const useDeviceStore = create<DeviceStoreState>()(
  persist(
    (set, get) => ({
      // 设备信息
      deviceId: null,
      deviceName: '',
      deviceType: 'mobile',
      isRegistered: false,
      registrationError: null,

      // 配对信息
      pairedDevices: [],
      currentSession: null,
      pairingCode: '',
      isPairing: false,
      pairingError: null,

      // 设备能力
      capabilities: {
        speechRecognition: false,
        mediaRecording: false,
        camera: false,
        vibration: false
      },

      // 初始化设备
      initializeDevice: async () => {
        try {
          let { deviceId } = get()
          
          // 如果没有设备ID，生成一个新的
          if (!deviceId) {
            deviceId = generateDeviceId()
          }

          // 获取设备信息
          const deviceInfo = await getDeviceInfo()
          
          // 检测设备能力
          const capabilities = await get().detectCapabilities()

          set({
            deviceId,
            deviceName: deviceInfo.name,
            deviceType: 'mobile',
            capabilities
          })

          return { deviceId, deviceInfo, capabilities }
        } catch (error) {
          console.error('设备初始化失败:', error)
          set({ registrationError: error.message })
          throw error
        }
      },

      // 检测设备能力
      detectCapabilities: async () => {
        const capabilities = {
          speechRecognition: false,
          mediaRecording: false,
          camera: false,
          vibration: false
        }

        try {
          // 检测语音识别支持
          capabilities.speechRecognition = 
            'webkitSpeechRecognition' in window || 'SpeechRecognition' in window

          // 检测媒体录制支持
          capabilities.mediaRecording = 
            navigator.mediaDevices && 
            typeof navigator.mediaDevices.getUserMedia === 'function'

          // 检测摄像头支持
          if (capabilities.mediaRecording) {
            try {
              const devices = await navigator.mediaDevices.enumerateDevices()
              capabilities.camera = devices.some(device => device.kind === 'videoinput')
            } catch (error) {
              console.warn('无法检测摄像头:', error)
            }
          }

          // 检测震动支持
          capabilities.vibration = 'vibrate' in navigator

        } catch (error) {
          console.error('检测设备能力失败:', error)
        }

        return capabilities
      },

      // 注册设备
      registerDevice: (socket) => {
        return new Promise((resolve, reject) => {
          const { deviceId, deviceName, deviceType, capabilities } = get()
          
          if (!socket) {
            reject(new Error('Socket连接不可用'))
            return
          }

          const deviceInfo = {
            deviceId,
            type: deviceType,
            name: deviceName,
            capabilities: Object.keys(capabilities).filter(key => capabilities[key]),
            metadata: {
              userAgent: navigator.userAgent,
              platform: navigator.platform,
              language: navigator.language,
              timestamp: new Date().toISOString()
            }
          }

          // 监听注册结果
          const handleRegistered = (response) => {
            if (response.success) {
              set({ 
                isRegistered: true,
                registrationError: null,
                deviceId: response.deviceId
              })
              resolve(response)
            } else {
              set({ 
                isRegistered: false,
                registrationError: response.error
              })
              reject(new Error(response.error))
            }
            socket.off('device:registered', handleRegistered)
          }

          socket.on('device:registered', handleRegistered)
          socket.emit('device:register', deviceInfo)

          // 超时处理
          setTimeout(() => {
            socket.off('device:registered', handleRegistered)
            reject(new Error('设备注册超时'))
          }, 10000)
        })
      },

      // 开始配对
      startPairing: (pairingCode) => {
        return new Promise((resolve, reject) => {
          const { deviceId } = get()
          
          set({ 
            isPairing: true, 
            pairingError: null,
            pairingCode 
          })

          // 这里需要通过socket发送配对请求
          // 实际实现需要在组件中调用
          resolve({ success: true, pairingCode })
        })
      },

      // 配对成功
      onPairingSuccess: (sessionData) => {
        const { devices, sessionId } = sessionData
        
        set({
          isPairing: false,
          pairingError: null,
          currentSession: sessionId,
          pairedDevices: devices.filter(d => d.id !== get().deviceId)
        })
      },

      // 配对失败
      onPairingError: (error) => {
        set({
          isPairing: false,
          pairingError: error,
          pairingCode: ''
        })
      },

      // 断开配对
      disconnectPairing: () => {
        set({
          currentSession: null,
          pairedDevices: [],
          pairingCode: ''
        })
      },

      // 更新设备名称
      updateDeviceName: (name) => {
        set({ deviceName: name })
      },

      // 获取配对状态
      getPairingStatus: () => {
        const { currentSession, pairedDevices, isPairing } = get()
        
        if (isPairing) return 'pairing'
        if (currentSession && pairedDevices.length > 0) return 'paired'
        return 'unpaired'
      },

      // 重置设备状态
      resetDevice: () => {
        set({
          isRegistered: false,
          registrationError: null,
          currentSession: null,
          pairedDevices: [],
          pairingCode: '',
          isPairing: false,
          pairingError: null
        })
      },

      // 获取设备统计信息
      getDeviceStats: () => {
        const { deviceId, deviceName, deviceType, capabilities, pairedDevices, currentSession } = get()
        
        return {
          deviceId,
          deviceName,
          deviceType,
          capabilities,
          pairedDevicesCount: pairedDevices.length,
          hasActiveSession: !!currentSession,
          supportedFeatures: Object.keys(capabilities).filter(key => capabilities[key])
        }
      }
    }),
    {
      name: 'smartinput-device',
      partialize: (state) => ({
        deviceId: state.deviceId,
        deviceName: state.deviceName,
        deviceType: state.deviceType,
        capabilities: state.capabilities
      })
    }
  )
)
