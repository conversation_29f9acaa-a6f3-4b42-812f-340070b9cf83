import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { io, Socket } from 'socket.io-client'
import type { ServerToClientEvents, ClientToServerEvents } from '../types'

const SOCKET_URL = import.meta.env.VITE_SOCKET_URL || 'ws://localhost:3001'

type SocketClient = Socket<ServerToClientEvents, ClientToServerEvents>

interface SocketStoreState {
  // 状态
  socket: SocketClient | null;
  isConnected: boolean;
  isConnecting: boolean;
  connectionError: string | null;
  lastPing: number | null;
  reconnectAttempts: number;
  maxReconnectAttempts: number;

  // 方法
  connect: () => void;
  disconnect: () => void;
  emit: (event: string, data?: any) => boolean;
  on: (event: string, callback: (...args: any[]) => void) => () => void;
  off: (event: string, callback?: (...args: any[]) => void) => void;
  ping: () => void;
  getConnectionStatus: () => 'connected' | 'connecting' | 'error' | 'reconnecting' | 'disconnected';
  resetConnection: () => void;
  forceReconnect: () => void;
}

export const useSocketStore = create<SocketStoreState>()(
  subscribeWithSelector((set, get) => ({
    // 状态
    socket: null,
    isConnected: false,
    isConnecting: false,
    connectionError: null,
    lastPing: null,
    reconnectAttempts: 0,
    maxReconnectAttempts: 5,

    // 连接方法
    connect: () => {
      const { isConnected, isConnecting } = get()
      
      if (isConnected || isConnecting) {
        return
      }

      set({ isConnecting: true, connectionError: null })

      try {
        const newSocket: SocketClient = io(SOCKET_URL, {
          transports: ['websocket', 'polling'],
          timeout: 10000,
          reconnection: true,
          reconnectionAttempts: 5,
          reconnectionDelay: 1000,
          reconnectionDelayMax: 5000,
          // maxHttpBufferSize: 1e8, // 移除不支持的选项
          // pingTimeout: 60000, // 移除不支持的选项
          // pingInterval: 25000 // 移除不支持的选项
        })

        // 连接成功
        newSocket.on('connect', () => {
          console.log('Socket connected:', newSocket.id)
          set({ 
            socket: newSocket,
            isConnected: true, 
            isConnecting: false,
            connectionError: null,
            reconnectAttempts: 0
          })
        })

        // 连接失败
        newSocket.on('connect_error', (error) => {
          console.error('Socket connection error:', error)
          set(state => ({ 
            isConnecting: false,
            connectionError: error.message,
            reconnectAttempts: state.reconnectAttempts + 1
          }))
        })

        // 断开连接
        newSocket.on('disconnect', (reason) => {
          console.log('Socket disconnected:', reason)
          set({ 
            isConnected: false,
            socket: null
          })
        })

        // 心跳检测 - 使用any类型处理Socket.io内置事件
        ;(newSocket as any).on('pong', () => {
          set({ lastPing: Date.now() })
        })

        // 重连事件
        ;(newSocket as any).on('reconnect', (attemptNumber: number) => {
          console.log('Socket reconnected after', attemptNumber, 'attempts')
          set({ reconnectAttempts: 0 })
        })

        ;(newSocket as any).on('reconnect_attempt', (attemptNumber: number) => {
          console.log('Socket reconnect attempt:', attemptNumber)
          set({ reconnectAttempts: attemptNumber })
        })

        ;(newSocket as any).on('reconnect_failed', () => {
          console.error('Socket reconnection failed')
          set({
            connectionError: '重连失败，请检查网络连接',
            isConnecting: false
          })
        })

        set({ socket: newSocket })

      } catch (error) {
        console.error('Failed to create socket:', error)
        const errorMessage = error instanceof Error ? error.message : 'Socket连接失败'
        set({
          isConnecting: false,
          connectionError: errorMessage
        })
      }
    },

    // 断开连接
    disconnect: () => {
      const { socket } = get()
      if (socket) {
        socket.disconnect()
        set({ 
          socket: null,
          isConnected: false,
          isConnecting: false,
          connectionError: null
        })
      }
    },

    // 发送消息
    emit: (event: string, data?: any) => {
      const { socket, isConnected } = get()
      if (socket && isConnected) {
        socket.emit(event as any, data)
        return true
      }
      console.warn('Socket not connected, cannot emit:', event)
      return false
    },

    // 监听事件
    on: (event: string, callback: (...args: any[]) => void) => {
      const { socket } = get()
      if (socket) {
        socket.on(event as any, callback)
        return () => socket.off(event as any, callback)
      }
      return () => {}
    },

    // 移除事件监听
    off: (event: string, callback?: (...args: any[]) => void) => {
      const { socket } = get()
      if (socket) {
        if (callback) {
          socket.off(event as any, callback)
        } else {
          socket.off(event as any)
        }
      }
    },

    // 发送心跳
    ping: () => {
      const { socket, isConnected } = get()
      if (socket && isConnected) {
        ;(socket as any).emit('ping')
      }
    },

    // 获取连接状态
    getConnectionStatus: () => {
      const { isConnected, isConnecting, connectionError, reconnectAttempts } = get()
      
      if (isConnected) return 'connected'
      if (isConnecting) return 'connecting'
      if (connectionError) return 'error'
      if (reconnectAttempts > 0) return 'reconnecting'
      return 'disconnected'
    },

    // 重置连接状态
    resetConnection: () => {
      set({
        connectionError: null,
        reconnectAttempts: 0
      })
    },

    // 强制重连
    forceReconnect: () => {
      const { disconnect, connect } = get()
      disconnect()
      setTimeout(() => {
        connect()
      }, 1000)
    }
  }))
)

// 自动心跳检测
setInterval(() => {
  const { ping, isConnected } = useSocketStore.getState()
  if (isConnected) {
    ping()
  }
}, 30000) // 每30秒发送一次心跳
