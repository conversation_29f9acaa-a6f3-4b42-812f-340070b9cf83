import React, { useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Mic, Smartphone, Monitor, QrCode, Settings, Zap } from 'lucide-react'
import { useDeviceStore } from '../stores/deviceStore'
import { useSocketStore } from '../stores/socketStore'

const HomePage = () => {
  const { 
    deviceId, 
    deviceName, 
    isRegistered, 
    pairedDevices, 
    getPairingStatus,
    initializeDevice,
    registerDevice 
  } = useDeviceStore()
  
  const { socket, isConnected } = useSocketStore()

  const pairingStatus = getPairingStatus()

  useEffect(() => {
    // 初始化设备
    const init = async () => {
      try {
        if (!deviceId) {
          await initializeDevice()
        }
        
        // 如果socket连接但设备未注册，自动注册
        if (socket && isConnected && !isRegistered) {
          await registerDevice(socket)
        }
      } catch (error) {
        console.error('设备初始化失败:', error)
      }
    }

    init()
  }, [socket, isConnected, deviceId, isRegistered, initializeDevice, registerDevice])

  const getQuickActions = () => {
    const actions = []

    // 语音输入
    if (pairingStatus === 'paired') {
      actions.push({
        id: 'voice',
        title: '开始语音输入',
        description: '按住录音，松开发送',
        icon: <Mic className="w-6 h-6" />,
        link: '/voice',
        color: 'bg-blue-600 hover:bg-blue-700',
        primary: true
      })
    }

    // 设备配对
    if (pairingStatus === 'unpaired') {
      actions.push({
        id: 'pairing',
        title: '配对设备',
        description: '扫描二维码连接设备',
        icon: <QrCode className="w-6 h-6" />,
        link: '/pairing',
        color: 'bg-green-600 hover:bg-green-700',
        primary: true
      })
    }

    // 设置
    actions.push({
      id: 'settings',
      title: '设置',
      description: '调整应用设置',
      icon: <Settings className="w-6 h-6" />,
      link: '/settings',
      color: 'bg-gray-600 hover:bg-gray-700'
    })

    return actions
  }

  const quickActions = getQuickActions()

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-md mx-auto space-y-6">
        
        {/* 欢迎卡片 */}
        <div className="card">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Smartphone className="w-8 h-8 text-blue-600" />
            </div>
            <h2 className="text-xl font-bold text-gray-900 mb-2">
              欢迎使用 SmartInput
            </h2>
            <p className="text-gray-600 text-sm">
              通过语音输入，让文字输入更高效
            </p>
          </div>
        </div>

        {/* 状态卡片 */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">设备状态</h3>
          
          <div className="space-y-3">
            {/* 连接状态 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={`w-3 h-3 rounded-full ${
                  isConnected ? 'bg-green-500' : 'bg-red-500'
                }`} />
                <span className="text-sm text-gray-700">服务器连接</span>
              </div>
              <span className={`text-sm font-medium ${
                isConnected ? 'text-green-600' : 'text-red-600'
              }`}>
                {isConnected ? '已连接' : '未连接'}
              </span>
            </div>

            {/* 设备注册状态 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={`w-3 h-3 rounded-full ${
                  isRegistered ? 'bg-green-500' : 'bg-yellow-500'
                }`} />
                <span className="text-sm text-gray-700">设备注册</span>
              </div>
              <span className={`text-sm font-medium ${
                isRegistered ? 'text-green-600' : 'text-yellow-600'
              }`}>
                {isRegistered ? '已注册' : '未注册'}
              </span>
            </div>

            {/* 配对状态 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={`w-3 h-3 rounded-full ${
                  pairingStatus === 'paired' ? 'bg-green-500' : 
                  pairingStatus === 'pairing' ? 'bg-yellow-500' : 'bg-gray-400'
                }`} />
                <span className="text-sm text-gray-700">设备配对</span>
              </div>
              <span className={`text-sm font-medium ${
                pairingStatus === 'paired' ? 'text-green-600' : 
                pairingStatus === 'pairing' ? 'text-yellow-600' : 'text-gray-600'
              }`}>
                {pairingStatus === 'paired' ? `已配对 (${pairedDevices.length})` : 
                 pairingStatus === 'pairing' ? '配对中' : '未配对'}
              </span>
            </div>
          </div>
        </div>

        {/* 配对设备列表 */}
        {pairedDevices.length > 0 && (
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">已配对设备</h3>
            <div className="space-y-2">
              {pairedDevices.map((device) => (
                <div key={device.id} className="flex items-center space-x-3 p-2 bg-gray-50 rounded-lg">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    {device.type === 'browser' ? (
                      <Monitor className="w-4 h-4 text-blue-600" />
                    ) : (
                      <Smartphone className="w-4 h-4 text-blue-600" />
                    )}
                  </div>
                  <div className="flex-1">
                    <div className="text-sm font-medium text-gray-900">
                      {device.name}
                    </div>
                    <div className="text-xs text-gray-500">
                      {device.type === 'browser' ? '浏览器' : '桌面应用'}
                    </div>
                  </div>
                  <div className="w-2 h-2 bg-green-500 rounded-full" />
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 快捷操作 */}
        <div className="space-y-3">
          {quickActions.map((action) => (
            <Link
              key={action.id}
              to={action.link}
              className={`block w-full p-4 rounded-xl text-white transition-colors ${action.color} ${
                action.primary ? 'shadow-lg' : ''
              }`}
            >
              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0">
                  {action.icon}
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold">{action.title}</h3>
                  <p className="text-sm opacity-90">{action.description}</p>
                </div>
                <div className="flex-shrink-0">
                  <Zap className="w-5 h-5" />
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* 使用提示 */}
        <div className="card bg-blue-50 border-blue-200">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-blue-900 mb-2">使用提示</h3>
            <div className="text-sm text-blue-700 space-y-1">
              <p>1. 首先配对您的电脑或浏览器</p>
              <p>2. 然后就可以开始语音输入了</p>
              <p>3. 语音会自动转换为文字并发送到配对设备</p>
            </div>
          </div>
        </div>

      </div>
    </div>
  )
}

export default HomePage
