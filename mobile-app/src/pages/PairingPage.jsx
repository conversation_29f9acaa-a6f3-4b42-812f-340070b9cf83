import React, { useState, useEffect, useRef } from 'react'
import { QrCode, Camera, Loader2, CheckCircle, XCircle, RefreshCw } from 'lucide-react'
import QrScanner from 'qr-scanner'
import { useDeviceStore } from '../stores/deviceStore'
import { useSocketStore } from '../stores/socketStore'

const PairingPage = () => {
  const [scannerActive, setScannerActive] = useState(false)
  const [scanError, setScanError] = useState(null)
  const [manualCode, setManualCode] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)
  const videoRef = useRef(null)
  const qrScannerRef = useRef(null)

  const { 
    isPairing, 
    pairingError, 
    pairingCode,
    startPairing,
    onPairingSuccess,
    onPairingError 
  } = useDeviceStore()
  
  const { socket, emit, on, off } = useSocketStore()

  useEffect(() => {
    // 监听配对结果
    const handlePairingResult = (result) => {
      setIsProcessing(false)
      
      if (result.success) {
        onPairingSuccess(result)
      } else {
        onPairingError(result.error || '配对失败')
      }
    }

    if (socket) {
      on('device:pair:result', handlePairingResult)
    }

    return () => {
      if (socket) {
        off('device:pair:result', handlePairingResult)
      }
    }
  }, [socket, on, off, onPairingSuccess, onPairingError])

  // 启动二维码扫描
  const startScanner = async () => {
    try {
      setScanError(null)
      
      if (!videoRef.current) {
        throw new Error('视频元素未准备好')
      }

      // 检查摄像头权限
      const hasCamera = await QrScanner.hasCamera()
      if (!hasCamera) {
        throw new Error('未检测到摄像头')
      }

      // 创建扫描器
      qrScannerRef.current = new QrScanner(
        videoRef.current,
        (result) => {
          console.log('扫描到二维码:', result.data)
          handleQRCodeScanned(result.data)
        },
        {
          onDecodeError: (error) => {
            // 忽略解码错误，继续扫描
            console.debug('QR解码错误:', error)
          },
          highlightScanRegion: true,
          highlightCodeOutline: true,
          preferredCamera: 'environment' // 后置摄像头
        }
      )

      await qrScannerRef.current.start()
      setScannerActive(true)
      
    } catch (error) {
      console.error('启动扫描器失败:', error)
      setScanError(error.message)
    }
  }

  // 停止二维码扫描
  const stopScanner = () => {
    if (qrScannerRef.current) {
      qrScannerRef.current.stop()
      qrScannerRef.current.destroy()
      qrScannerRef.current = null
    }
    setScannerActive(false)
  }

  // 处理二维码扫描结果
  const handleQRCodeScanned = async (data) => {
    try {
      stopScanner()
      
      // 解析二维码数据
      const pairingData = JSON.parse(data)
      
      if (pairingData.type !== 'smartinput_pairing') {
        throw new Error('无效的配对二维码')
      }

      await processPairing(pairingData.code)
      
    } catch (error) {
      console.error('处理二维码失败:', error)
      setScanError('无效的二维码格式')
      setTimeout(() => {
        startScanner() // 重新开始扫描
      }, 2000)
    }
  }

  // 处理配对
  const processPairing = async (code) => {
    if (!socket) {
      onPairingError('服务器连接不可用')
      return
    }

    try {
      setIsProcessing(true)
      await startPairing(code)
      
      // 发送配对请求
      emit('device:pair', { pairingCode: code })
      
    } catch (error) {
      setIsProcessing(false)
      onPairingError(error.message)
    }
  }

  // 手动输入配对码
  const handleManualPairing = async () => {
    if (!manualCode.trim()) {
      setScanError('请输入配对码')
      return
    }

    await processPairing(manualCode.trim().toUpperCase())
  }

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      stopScanner()
    }
  }, [])

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-md mx-auto space-y-6">
        
        {/* 标题 */}
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">设备配对</h1>
          <p className="text-gray-600">扫描电脑或浏览器上的二维码进行配对</p>
        </div>

        {/* 扫描区域 */}
        <div className="card">
          <div className="text-center">
            {!scannerActive ? (
              <div className="space-y-4">
                <div className="w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                  <QrCode className="w-12 h-12 text-blue-600" />
                </div>
                <button
                  onClick={startScanner}
                  disabled={isProcessing}
                  className="btn btn-primary btn-lg w-full"
                >
                  <Camera className="w-5 h-5 mr-2" />
                  开始扫描二维码
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="relative">
                  <video
                    ref={videoRef}
                    className="w-full h-64 bg-black rounded-lg object-cover"
                    playsInline
                    muted
                  />
                  {isProcessing && (
                    <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg">
                      <div className="text-white text-center">
                        <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2" />
                        <p>处理中...</p>
                      </div>
                    </div>
                  )}
                </div>
                <button
                  onClick={stopScanner}
                  className="btn btn-secondary w-full"
                >
                  停止扫描
                </button>
              </div>
            )}
          </div>
        </div>

        {/* 手动输入 */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">手动输入配对码</h3>
          <div className="space-y-3">
            <input
              type="text"
              value={manualCode}
              onChange={(e) => setManualCode(e.target.value.toUpperCase())}
              placeholder="输入8位配对码"
              className="input"
              maxLength={8}
              disabled={isProcessing}
            />
            <button
              onClick={handleManualPairing}
              disabled={!manualCode.trim() || isProcessing}
              className="btn btn-primary w-full"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  配对中...
                </>
              ) : (
                '开始配对'
              )}
            </button>
          </div>
        </div>

        {/* 错误提示 */}
        {(scanError || pairingError) && (
          <div className="card bg-red-50 border-red-200">
            <div className="flex items-center space-x-3">
              <XCircle className="w-5 h-5 text-red-600 flex-shrink-0" />
              <div>
                <h4 className="text-sm font-medium text-red-800">配对失败</h4>
                <p className="text-sm text-red-600 mt-1">
                  {scanError || pairingError}
                </p>
              </div>
            </div>
            <button
              onClick={() => {
                setScanError(null)
                onPairingError(null)
              }}
              className="mt-3 text-sm text-red-600 hover:text-red-800"
            >
              <RefreshCw className="w-4 h-4 inline mr-1" />
              重试
            </button>
          </div>
        )}

        {/* 配对成功 */}
        {isPairing && !pairingError && (
          <div className="card bg-green-50 border-green-200">
            <div className="flex items-center space-x-3">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <div>
                <h4 className="text-sm font-medium text-green-800">配对成功</h4>
                <p className="text-sm text-green-600 mt-1">
                  设备已成功配对，可以开始使用语音输入功能
                </p>
              </div>
            </div>
          </div>
        )}

        {/* 使用说明 */}
        <div className="card bg-blue-50 border-blue-200">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">配对步骤</h3>
          <div className="space-y-2 text-sm text-blue-700">
            <div className="flex items-start space-x-2">
              <span className="w-5 h-5 bg-blue-200 rounded-full flex items-center justify-center text-xs font-bold text-blue-800 flex-shrink-0 mt-0.5">1</span>
              <p>在电脑浏览器中安装SmartInput扩展</p>
            </div>
            <div className="flex items-start space-x-2">
              <span className="w-5 h-5 bg-blue-200 rounded-full flex items-center justify-center text-xs font-bold text-blue-800 flex-shrink-0 mt-0.5">2</span>
              <p>点击扩展图标，生成配对二维码</p>
            </div>
            <div className="flex items-start space-x-2">
              <span className="w-5 h-5 bg-blue-200 rounded-full flex items-center justify-center text-xs font-bold text-blue-800 flex-shrink-0 mt-0.5">3</span>
              <p>使用手机扫描二维码完成配对</p>
            </div>
          </div>
        </div>

      </div>
    </div>
  )
}

export default PairingPage
