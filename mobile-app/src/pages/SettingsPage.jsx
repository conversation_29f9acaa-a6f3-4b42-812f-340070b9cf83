import React, { useState } from 'react'
import { 
  Settings, 
  Mic, 
  Globe, 
  Zap, 
  Volume2, 
  Smartphone, 
  Trash2,
  Download,
  Upload,
  RefreshCw,
  Info
} from 'lucide-react'
import { useVoiceStore } from '../stores/voiceStore'
import { useDeviceStore } from '../stores/deviceStore'
import { useSocketStore } from '../stores/socketStore'
import speechService from '../services/speechService'

const SettingsPage = () => {
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [exportData, setExportData] = useState(null)

  const {
    language,
    settings,
    voiceHistory,
    setLanguage,
    updateSettings,
    clearHistory,
    getStats
  } = useVoiceStore()

  const {
    deviceName,
    capabilities,
    updateDeviceName,
    resetDevice,
    getDeviceStats
  } = useDeviceStore()

  const { 
    serverUrl, 
    isConnected, 
    connectionError,
    updateServerUrl,
    forceReconnect 
  } = useSocketStore()

  const stats = getStats()
  const deviceStats = getDeviceStats()
  const supportedLanguages = speechService.getSupportedLanguages()

  // 更新语言设置
  const handleLanguageChange = (newLanguage) => {
    setLanguage(newLanguage)
    speechService.updateConfig({ language: newLanguage })
  }

  // 更新语音设置
  const handleSettingChange = (key, value) => {
    const newSettings = { ...settings, [key]: value }
    updateSettings(newSettings)
    
    // 更新语音识别配置
    if (['continuous', 'interimResults', 'maxAlternatives'].includes(key)) {
      speechService.updateConfig({ [key]: value })
    }
  }

  // 导出数据
  const handleExportData = () => {
    const data = {
      version: '1.0',
      timestamp: new Date().toISOString(),
      device: deviceStats,
      settings: {
        language,
        voiceSettings: settings
      },
      history: voiceHistory,
      stats
    }
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    
    const a = document.createElement('a')
    a.href = url
    a.download = `smartinput-data-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // 导入数据
  const handleImportData = (event) => {
    const file = event.target.files[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target.result)
        
        if (data.settings?.language) {
          handleLanguageChange(data.settings.language)
        }
        
        if (data.settings?.voiceSettings) {
          updateSettings(data.settings.voiceSettings)
        }
        
        alert('数据导入成功')
      } catch (error) {
        alert('数据导入失败：文件格式错误')
      }
    }
    reader.readAsText(file)
  }

  // 重置所有设置
  const handleResetAll = () => {
    if (confirm('确定要重置所有设置吗？这将清除所有数据和配对信息。')) {
      clearHistory()
      resetDevice()
      updateSettings({
        autoSend: true,
        continuous: false,
        interimResults: true,
        maxAlternatives: 1,
        noiseReduction: true,
        echoCancellation: true,
        autoGainControl: true
      })
      setLanguage('zh-CN')
      alert('设置已重置')
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-md mx-auto space-y-6">

        {/* 语音设置 */}
        <div className="card">
          <div className="flex items-center space-x-2 mb-4">
            <Mic className="w-5 h-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900">语音设置</h3>
          </div>

          <div className="space-y-4">
            {/* 语言选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                识别语言
              </label>
              <select
                value={language}
                onChange={(e) => handleLanguageChange(e.target.value)}
                className="input"
              >
                {supportedLanguages.map((lang) => (
                  <option key={lang.code} value={lang.code}>
                    {lang.name}
                  </option>
                ))}
              </select>
            </div>

            {/* 自动发送 */}
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">自动发送</label>
                <p className="text-xs text-gray-500">识别完成后自动发送文字</p>
              </div>
              <label className="switch">
                <input
                  type="checkbox"
                  checked={settings.autoSend}
                  onChange={(e) => handleSettingChange('autoSend', e.target.checked)}
                />
                <span className="slider"></span>
              </label>
            </div>

            {/* 连续识别 */}
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">连续识别</label>
                <p className="text-xs text-gray-500">持续监听语音输入</p>
              </div>
              <label className="switch">
                <input
                  type="checkbox"
                  checked={settings.continuous}
                  onChange={(e) => handleSettingChange('continuous', e.target.checked)}
                />
                <span className="slider"></span>
              </label>
            </div>

            {/* 实时结果 */}
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">实时结果</label>
                <p className="text-xs text-gray-500">显示识别过程中的临时结果</p>
              </div>
              <label className="switch">
                <input
                  type="checkbox"
                  checked={settings.interimResults}
                  onChange={(e) => handleSettingChange('interimResults', e.target.checked)}
                />
                <span className="slider"></span>
              </label>
            </div>
          </div>
        </div>

        {/* 设备设置 */}
        <div className="card">
          <div className="flex items-center space-x-2 mb-4">
            <Smartphone className="w-5 h-5 text-green-600" />
            <h3 className="text-lg font-semibold text-gray-900">设备设置</h3>
          </div>

          <div className="space-y-4">
            {/* 设备名称 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                设备名称
              </label>
              <input
                type="text"
                value={deviceName}
                onChange={(e) => updateDeviceName(e.target.value)}
                className="input"
                placeholder="输入设备名称"
              />
            </div>

            {/* 服务器地址 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                服务器地址
              </label>
              <div className="flex space-x-2">
                <input
                  type="url"
                  value={serverUrl}
                  onChange={(e) => updateServerUrl(e.target.value)}
                  className="input flex-1"
                  placeholder="ws://localhost:3001"
                />
                <button
                  onClick={forceReconnect}
                  className="btn btn-secondary"
                  title="重新连接"
                >
                  <RefreshCw className="w-4 h-4" />
                </button>
              </div>
              <div className="mt-1 flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${
                  isConnected ? 'bg-green-500' : 'bg-red-500'
                }`} />
                <span className="text-xs text-gray-500">
                  {isConnected ? '已连接' : connectionError || '未连接'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* 统计信息 */}
        <div className="card">
          <div className="flex items-center space-x-2 mb-4">
            <Info className="w-5 h-5 text-purple-600" />
            <h3 className="text-lg font-semibold text-gray-900">使用统计</h3>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{stats.totalRecordings}</div>
              <div className="text-xs text-gray-500">总录音次数</div>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {Math.round(stats.averageConfidence * 100)}%
              </div>
              <div className="text-xs text-gray-500">平均准确率</div>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">{deviceStats.pairedDevicesCount}</div>
              <div className="text-xs text-gray-500">配对设备</div>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">{deviceStats.supportedFeatures.length}</div>
              <div className="text-xs text-gray-500">支持功能</div>
            </div>
          </div>
        </div>

        {/* 高级设置 */}
        <div className="card">
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="flex items-center justify-between w-full text-left"
          >
            <div className="flex items-center space-x-2">
              <Settings className="w-5 h-5 text-gray-600" />
              <h3 className="text-lg font-semibold text-gray-900">高级设置</h3>
            </div>
            <div className={`transform transition-transform ${showAdvanced ? 'rotate-180' : ''}`}>
              ▼
            </div>
          </button>

          {showAdvanced && (
            <div className="mt-4 space-y-4 border-t pt-4">
              {/* 音频增强 */}
              <div className="space-y-3">
                <h4 className="text-sm font-semibold text-gray-700">音频增强</h4>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">噪音抑制</span>
                  <label className="switch">
                    <input
                      type="checkbox"
                      checked={settings.noiseReduction}
                      onChange={(e) => handleSettingChange('noiseReduction', e.target.checked)}
                    />
                    <span className="slider"></span>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">回声消除</span>
                  <label className="switch">
                    <input
                      type="checkbox"
                      checked={settings.echoCancellation}
                      onChange={(e) => handleSettingChange('echoCancellation', e.target.checked)}
                    />
                    <span className="slider"></span>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">自动增益</span>
                  <label className="switch">
                    <input
                      type="checkbox"
                      checked={settings.autoGainControl}
                      onChange={(e) => handleSettingChange('autoGainControl', e.target.checked)}
                    />
                    <span className="slider"></span>
                  </label>
                </div>
              </div>

              {/* 数据管理 */}
              <div className="space-y-3">
                <h4 className="text-sm font-semibold text-gray-700">数据管理</h4>
                
                <div className="flex space-x-2">
                  <button
                    onClick={handleExportData}
                    className="btn btn-secondary flex-1"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    导出数据
                  </button>
                  
                  <label className="btn btn-secondary flex-1 cursor-pointer">
                    <Upload className="w-4 h-4 mr-2" />
                    导入数据
                    <input
                      type="file"
                      accept=".json"
                      onChange={handleImportData}
                      className="hidden"
                    />
                  </label>
                </div>

                <button
                  onClick={() => clearHistory()}
                  className="btn btn-danger w-full"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  清除历史记录
                </button>

                <button
                  onClick={handleResetAll}
                  className="btn btn-danger w-full"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  重置所有设置
                </button>
              </div>
            </div>
          )}
        </div>

        {/* 设备能力 */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">设备能力</h3>
          <div className="grid grid-cols-2 gap-2 text-xs">
            {Object.entries(capabilities).map(([key, supported]) => (
              <div key={key} className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${
                  supported ? 'bg-green-500' : 'bg-gray-300'
                }`} />
                <span className={supported ? 'text-gray-700' : 'text-gray-400'}>
                  {key}
                </span>
              </div>
            ))}
          </div>
        </div>

      </div>
    </div>
  )
}

export default SettingsPage
