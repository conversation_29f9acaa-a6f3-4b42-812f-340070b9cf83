import React from 'react'
import ReactDOM from 'react-dom/client'
import { BrowserRouter } from 'react-router-dom'
import App from './App.jsx'
import './index.css'

// 注册 Service Worker
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('SW registered: ', registration);
      })
      .catch((registrationError) => {
        console.log('SW registration failed: ', registrationError);
      });
  });
}

// 检查是否支持语音识别
if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
  console.warn('当前浏览器不支持语音识别功能');
}

// 检查是否支持媒体录制
if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
  console.warn('当前浏览器不支持媒体录制功能');
}

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <BrowserRouter>
      <App />
    </BrowserRouter>
  </React.StrictMode>,
)
