// 设备相关类型
export type DeviceType = 'mobile' | 'browser' | 'desktop';

export interface DeviceInfo {
  id: string;
  name: string;
  type: DeviceType;
  userAgent: string;
  capabilities: DeviceCapabilities;
  lastSeen: number;
  isOnline: boolean;
}

export interface DeviceCapabilities {
  supportsVoiceInput: boolean;
  supportsTextOutput: boolean;
  supportsFileUpload: boolean;
  maxFileSize?: number;
  supportedFormats?: string[];
}

export interface RegisteredDevice extends DeviceInfo {
  socketId: string;
  registeredAt: number;
}

// 配对相关类型
export interface PairingCode {
  code: string;
  deviceId: string;
  expiresAt: number;
  isUsed: boolean;
  createdAt: number;
}

export interface PairingRequest {
  code: string;
  deviceInfo: DeviceInfo;
}

export interface PairingData {
  success: boolean;
  deviceId?: string;
  targetDevice?: DeviceInfo;
  error?: string;
}

// 语音输入相关类型
export interface VoiceRecognitionResult {
  text: string;
  confidence: number;
  isFinal: boolean;
  language?: string;
  timestamp: number;
}

export interface VoiceInputData {
  text: string;
  confidence: number;
  language: string;
  deviceId: string;
  timestamp: number;
  metadata?: {
    duration?: number;
    audioFormat?: string;
    processingTime?: number;
  };
}

// Socket.io 事件类型
export interface ServerToClientEvents {
  'device-registered': (data: { success: boolean; deviceId: string; error?: string }) => void;
  'pairing-code-generated': (data: { code: string; qrCode: string; expiresAt: number }) => void;
  'pairing-success': (data: PairingData) => void;
  'pairing-failed': (data: { error: string }) => void;
  'voice-text': (data: VoiceInputData) => void;
  'device-connected': (data: DeviceInfo) => void;
  'device-disconnected': (data: { deviceId: string }) => void;
  'error': (data: { message: string; code?: string }) => void;
  'connection-status': (data: { connected: boolean; deviceCount: number }) => void;
}

export interface ClientToServerEvents {
  'register-device': (deviceInfo: DeviceInfo) => void;
  'generate-pairing-code': () => void;
  'pair-device': (request: PairingRequest) => void;
  'send-voice-text': (data: VoiceInputData) => void;
  'get-device-status': () => void;
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: number;
}

// 存储状态类型
export interface DeviceStoreState {
  currentDevice: DeviceInfo | null;
  pairedDevices: DeviceInfo[];
  isRegistered: boolean;
  registrationError: string | null;
  setCurrentDevice: (device: DeviceInfo | null) => void;
  addPairedDevice: (device: DeviceInfo) => void;
  removePairedDevice: (deviceId: string) => void;
  setRegistered: (registered: boolean) => void;
  setRegistrationError: (error: string | null) => void;
  clearDevices: () => void;
}

export interface SocketStoreState {
  socket: any; // Socket.io client instance
  isConnected: boolean;
  connectionError: string | null;
  lastPing: number | null;
  connect: () => void;
  disconnect: () => void;
  setConnectionError: (error: string | null) => void;
  updatePing: () => void;
}

export interface VoiceStoreState {
  isRecording: boolean;
  isProcessing: boolean;
  lastResult: VoiceRecognitionResult | null;
  error: string | null;
  language: string;
  confidence: number;
  startRecording: () => Promise<void>;
  stopRecording: () => void;
  setResult: (result: VoiceRecognitionResult) => void;
  setError: (error: string | null) => void;
  setLanguage: (language: string) => void;
  clearResult: () => void;
}

// 组件 Props 类型
export interface ToastProps {
  message: string;
  type: 'success' | 'error' | 'info' | 'warning';
  duration?: number;
  onClose: () => void;
}

export interface ConnectionStatusProps {
  isConnected: boolean;
  deviceCount?: number;
  lastPing?: number | null;
}

export interface HeaderProps {
  title: string;
  showBack?: boolean;
  onBack?: () => void;
  actions?: React.ReactNode;
}

// 语音识别相关类型
export interface SpeechRecognitionEvent extends Event {
  results: SpeechRecognitionResultList;
  resultIndex: number;
}

export interface SpeechRecognitionErrorEvent extends Event {
  error: string;
  message: string;
}

// 全局声明
declare global {
  interface Window {
    SpeechRecognition?: new () => SpeechRecognition;
    webkitSpeechRecognition?: new () => SpeechRecognition;
  }

  interface ImportMetaEnv {
    readonly VITE_SOCKET_URL?: string;
  }

  interface ImportMeta {
    readonly env: ImportMetaEnv;
  }
}

// 错误类型
export class SmartInputError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500
  ) {
    super(message);
    this.name = 'SmartInputError';
  }
}

// 配置类型
export interface AppConfig {
  serverUrl: string;
  apiTimeout: number;
  maxRetries: number;
  voiceRecognition: {
    language: string;
    continuous: boolean;
    interimResults: boolean;
    maxAlternatives: number;
  };
  pairing: {
    codeLength: number;
    expirationTime: number;
    maxAttempts: number;
  };
}
