/**
 * 生成唯一设备ID
 */
export function generateDeviceId() {
  const timestamp = Date.now().toString(36)
  const random = Math.random().toString(36).substr(2, 9)
  return `mobile_${timestamp}_${random}`
}

/**
 * 获取设备信息
 */
export async function getDeviceInfo() {
  const info = {
    name: getDeviceName(),
    type: 'mobile',
    platform: navigator.platform,
    userAgent: navigator.userAgent,
    language: navigator.language,
    languages: navigator.languages,
    online: navigator.onLine,
    cookieEnabled: navigator.cookieEnabled,
    screen: {
      width: screen.width,
      height: screen.height,
      colorDepth: screen.colorDepth,
      pixelDepth: screen.pixelDepth
    },
    viewport: {
      width: window.innerWidth,
      height: window.innerHeight
    },
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    timestamp: new Date().toISOString()
  }

  // 获取网络信息（如果支持）
  if ('connection' in navigator) {
    info.connection = {
      effectiveType: navigator.connection.effectiveType,
      downlink: navigator.connection.downlink,
      rtt: navigator.connection.rtt,
      saveData: navigator.connection.saveData
    }
  }

  return info
}

/**
 * 获取设备名称
 */
function getDeviceName() {
  const userAgent = navigator.userAgent

  // iOS设备
  if (/iPad/.test(userAgent)) {
    return 'iPad'
  }
  if (/iPhone/.test(userAgent)) {
    return 'iPhone'
  }
  if (/iPod/.test(userAgent)) {
    return 'iPod'
  }

  // Android设备
  if (/Android/.test(userAgent)) {
    const match = userAgent.match(/Android\s([0-9\.]+)/)
    const version = match ? match[1] : ''
    return `Android ${version}`.trim()
  }

  // Windows Phone
  if (/Windows Phone/.test(userAgent)) {
    return 'Windows Phone'
  }

  // 其他移动设备
  if (/Mobile/.test(userAgent)) {
    return 'Mobile Device'
  }

  // 桌面浏览器
  if (/Chrome/.test(userAgent)) {
    return 'Chrome Browser'
  }
  if (/Firefox/.test(userAgent)) {
    return 'Firefox Browser'
  }
  if (/Safari/.test(userAgent) && !/Chrome/.test(userAgent)) {
    return 'Safari Browser'
  }
  if (/Edge/.test(userAgent)) {
    return 'Edge Browser'
  }

  return 'Unknown Device'
}

/**
 * 检测是否为移动设备
 */
export function isMobileDevice() {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
}

/**
 * 检测是否为iOS设备
 */
export function isIOSDevice() {
  return /iPad|iPhone|iPod/.test(navigator.userAgent)
}

/**
 * 检测是否为Android设备
 */
export function isAndroidDevice() {
  return /Android/.test(navigator.userAgent)
}

/**
 * 检测是否支持触摸
 */
export function isTouchDevice() {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0
}

/**
 * 检测是否为PWA模式
 */
export function isPWAMode() {
  return window.matchMedia('(display-mode: standalone)').matches ||
         window.navigator.standalone === true
}

/**
 * 获取设备方向
 */
export function getDeviceOrientation() {
  if (screen.orientation) {
    return screen.orientation.type
  }
  
  // 备用方法
  if (window.innerHeight > window.innerWidth) {
    return 'portrait-primary'
  } else {
    return 'landscape-primary'
  }
}

/**
 * 检测网络状态
 */
export function getNetworkStatus() {
  const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection

  return {
    online: navigator.onLine,
    effectiveType: connection?.effectiveType || 'unknown',
    downlink: connection?.downlink || 0,
    rtt: connection?.rtt || 0,
    saveData: connection?.saveData || false
  }
}

/**
 * 震动反馈
 */
export function vibrate(pattern = 100) {
  if ('vibrate' in navigator) {
    navigator.vibrate(pattern)
  }
}

/**
 * 检测设备能力
 */
export async function detectDeviceCapabilities() {
  const capabilities = {
    speechRecognition: false,
    mediaRecording: false,
    camera: false,
    microphone: false,
    geolocation: false,
    notifications: false,
    vibration: false,
    fullscreen: false,
    clipboard: false,
    share: false
  }

  try {
    // 语音识别
    capabilities.speechRecognition = 
      'webkitSpeechRecognition' in window || 'SpeechRecognition' in window

    // 媒体录制
    capabilities.mediaRecording = 
      navigator.mediaDevices && 
      typeof navigator.mediaDevices.getUserMedia === 'function'

    // 地理位置
    capabilities.geolocation = 'geolocation' in navigator

    // 通知
    capabilities.notifications = 'Notification' in window

    // 震动
    capabilities.vibration = 'vibrate' in navigator

    // 全屏
    capabilities.fullscreen = 
      document.fullscreenEnabled || 
      document.webkitFullscreenEnabled || 
      document.mozFullScreenEnabled

    // 剪贴板
    capabilities.clipboard = 
      navigator.clipboard && 
      typeof navigator.clipboard.writeText === 'function'

    // 分享
    capabilities.share = 
      navigator.share && 
      typeof navigator.share === 'function'

    // 检测摄像头和麦克风
    if (capabilities.mediaRecording) {
      try {
        const devices = await navigator.mediaDevices.enumerateDevices()
        capabilities.camera = devices.some(device => device.kind === 'videoinput')
        capabilities.microphone = devices.some(device => device.kind === 'audioinput')
      } catch (error) {
        console.warn('无法枚举媒体设备:', error)
      }
    }

  } catch (error) {
    console.error('检测设备能力失败:', error)
  }

  return capabilities
}

/**
 * 请求设备权限
 */
export async function requestPermissions() {
  const permissions = {
    microphone: false,
    camera: false,
    notifications: false,
    geolocation: false
  }

  try {
    // 麦克风权限
    if (navigator.mediaDevices) {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
        permissions.microphone = true
        stream.getTracks().forEach(track => track.stop())
      } catch (error) {
        console.warn('麦克风权限被拒绝:', error)
      }
    }

    // 通知权限
    if ('Notification' in window) {
      const permission = await Notification.requestPermission()
      permissions.notifications = permission === 'granted'
    }

    // 地理位置权限
    if ('geolocation' in navigator) {
      try {
        await new Promise((resolve, reject) => {
          navigator.geolocation.getCurrentPosition(resolve, reject, { timeout: 5000 })
        })
        permissions.geolocation = true
      } catch (error) {
        console.warn('地理位置权限被拒绝:', error)
      }
    }

  } catch (error) {
    console.error('请求权限失败:', error)
  }

  return permissions
}

/**
 * 保存设备信息到本地存储
 */
export function saveDeviceInfo(deviceInfo) {
  try {
    localStorage.setItem('smartinput_device_info', JSON.stringify(deviceInfo))
  } catch (error) {
    console.error('保存设备信息失败:', error)
  }
}

/**
 * 从本地存储加载设备信息
 */
export function loadDeviceInfo() {
  try {
    const stored = localStorage.getItem('smartinput_device_info')
    return stored ? JSON.parse(stored) : null
  } catch (error) {
    console.error('加载设备信息失败:', error)
    return null
  }
}
