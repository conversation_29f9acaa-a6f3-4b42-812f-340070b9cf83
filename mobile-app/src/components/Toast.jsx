import React, { useState, useEffect } from 'react'
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react'

const Toast = ({ 
  message, 
  type = 'info', 
  duration = 3000, 
  onClose,
  position = 'top-center'
}) => {
  const [isVisible, setIsVisible] = useState(true)
  const [isLeaving, setIsLeaving] = useState(false)

  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        handleClose()
      }, duration)

      return () => clearTimeout(timer)
    }
  }, [duration])

  const handleClose = () => {
    setIsLeaving(true)
    setTimeout(() => {
      setIsVisible(false)
      onClose?.()
    }, 300) // 动画时间
  }

  if (!isVisible) return null

  const getTypeConfig = () => {
    switch (type) {
      case 'success':
        return {
          icon: <CheckCircle className="w-5 h-5" />,
          bgColor: 'bg-green-500',
          textColor: 'text-white'
        }
      case 'error':
        return {
          icon: <AlertCircle className="w-5 h-5" />,
          bgColor: 'bg-red-500',
          textColor: 'text-white'
        }
      case 'warning':
        return {
          icon: <AlertTriangle className="w-5 h-5" />,
          bgColor: 'bg-yellow-500',
          textColor: 'text-white'
        }
      default:
        return {
          icon: <Info className="w-5 h-5" />,
          bgColor: 'bg-blue-500',
          textColor: 'text-white'
        }
    }
  }

  const getPositionClasses = () => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4'
      case 'top-right':
        return 'top-4 right-4'
      case 'bottom-left':
        return 'bottom-4 left-4'
      case 'bottom-right':
        return 'bottom-4 right-4'
      case 'bottom-center':
        return 'bottom-4 left-1/2 transform -translate-x-1/2'
      default: // top-center
        return 'top-4 left-1/2 transform -translate-x-1/2'
    }
  }

  const config = getTypeConfig()

  return (
    <div
      className={`fixed z-50 ${getPositionClasses()} ${
        isLeaving ? 'animate-fade-out' : 'animate-fade-in'
      }`}
    >
      <div
        className={`${config.bgColor} ${config.textColor} rounded-lg shadow-lg p-4 max-w-sm mx-auto`}
      >
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            {config.icon}
          </div>
          <div className="flex-1">
            <p className="text-sm font-medium">{message}</p>
          </div>
          <button
            onClick={handleClose}
            className="flex-shrink-0 opacity-70 hover:opacity-100 transition-opacity"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  )
}

// Toast 管理器
class ToastManager {
  constructor() {
    this.toasts = []
    this.listeners = []
  }

  show(message, type = 'info', options = {}) {
    const toast = {
      id: Date.now() + Math.random(),
      message,
      type,
      duration: options.duration || 3000,
      position: options.position || 'top-center'
    }

    this.toasts.push(toast)
    this.notifyListeners()

    return toast.id
  }

  hide(id) {
    this.toasts = this.toasts.filter(toast => toast.id !== id)
    this.notifyListeners()
  }

  clear() {
    this.toasts = []
    this.notifyListeners()
  }

  success(message, options = {}) {
    return this.show(message, 'success', options)
  }

  error(message, options = {}) {
    return this.show(message, 'error', options)
  }

  warning(message, options = {}) {
    return this.show(message, 'warning', options)
  }

  info(message, options = {}) {
    return this.show(message, 'info', options)
  }

  subscribe(listener) {
    this.listeners.push(listener)
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener)
    }
  }

  notifyListeners() {
    this.listeners.forEach(listener => listener(this.toasts))
  }
}

// 全局 toast 实例
export const toast = new ToastManager()

// Toast 容器组件
export const ToastContainer = () => {
  const [toasts, setToasts] = useState([])

  useEffect(() => {
    const unsubscribe = toast.subscribe(setToasts)
    return unsubscribe
  }, [])

  return (
    <>
      {toasts.map((toastData) => (
        <Toast
          key={toastData.id}
          message={toastData.message}
          type={toastData.type}
          duration={toastData.duration}
          position={toastData.position}
          onClose={() => toast.hide(toastData.id)}
        />
      ))}
    </>
  )
}

export default Toast
