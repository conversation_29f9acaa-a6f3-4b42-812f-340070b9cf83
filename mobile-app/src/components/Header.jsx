import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import { Mic, Settings, Home, Smartphone } from 'lucide-react'
import { useDeviceStore } from '../stores/deviceStore'
import { useSocketStore } from '../stores/socketStore'

const Header = () => {
  const location = useLocation()
  const { deviceName, getPairingStatus } = useDeviceStore()
  const { isConnected } = useSocketStore()
  
  const pairingStatus = getPairingStatus()

  const getPageTitle = () => {
    switch (location.pathname) {
      case '/':
        return 'SmartInput'
      case '/pairing':
        return '设备配对'
      case '/voice':
        return '语音输入'
      case '/settings':
        return '设置'
      default:
        return 'SmartInput'
    }
  }

  const getStatusColor = () => {
    if (!isConnected) return 'text-red-500'
    if (pairingStatus === 'paired') return 'text-green-500'
    if (pairingStatus === 'pairing') return 'text-yellow-500'
    return 'text-gray-500'
  }

  const getStatusText = () => {
    if (!isConnected) return '未连接'
    if (pairingStatus === 'paired') return '已配对'
    if (pairingStatus === 'pairing') return '配对中'
    return '未配对'
  }

  return (
    <header className="bg-white shadow-sm border-b border-gray-200 safe-area-top">
      <div className="px-4 py-3">
        <div className="flex items-center justify-between">
          {/* 左侧：标题和状态 */}
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <Smartphone className="w-6 h-6 text-blue-600" />
              <h1 className="text-lg font-semibold text-gray-900">
                {getPageTitle()}
              </h1>
            </div>
            
            {/* 连接状态指示器 */}
            <div className="flex items-center space-x-1">
              <div className={`w-2 h-2 rounded-full ${
                isConnected ? 'bg-green-500' : 'bg-red-500'
              }`} />
              <span className={`text-xs font-medium ${getStatusColor()}`}>
                {getStatusText()}
              </span>
            </div>
          </div>

          {/* 右侧：导航菜单 */}
          <nav className="flex items-center space-x-1">
            <Link
              to="/"
              className={`p-2 rounded-lg transition-colors ${
                location.pathname === '/' 
                  ? 'bg-blue-100 text-blue-600' 
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
              title="首页"
            >
              <Home className="w-5 h-5" />
            </Link>
            
            <Link
              to="/voice"
              className={`p-2 rounded-lg transition-colors ${
                location.pathname === '/voice' 
                  ? 'bg-blue-100 text-blue-600' 
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
              title="语音输入"
            >
              <Mic className="w-5 h-5" />
            </Link>
            
            <Link
              to="/settings"
              className={`p-2 rounded-lg transition-colors ${
                location.pathname === '/settings' 
                  ? 'bg-blue-100 text-blue-600' 
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
              title="设置"
            >
              <Settings className="w-5 h-5" />
            </Link>
          </nav>
        </div>

        {/* 设备信息 */}
        {deviceName && (
          <div className="mt-2 text-xs text-gray-500">
            设备: {deviceName}
          </div>
        )}
      </div>
    </header>
  )
}

export default Header
