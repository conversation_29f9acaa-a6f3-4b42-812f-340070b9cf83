import React from 'react'
import { Wifi, WifiOff, Loader2, AlertCircle } from 'lucide-react'
import { useSocketStore } from '../stores/socketStore'

const ConnectionStatus = () => {
  const { 
    isConnected, 
    isConnecting, 
    connectionError, 
    reconnectAttempts,
    getConnectionStatus,
    forceReconnect 
  } = useSocketStore()

  const status = getConnectionStatus()

  // 如果连接正常且没有错误，不显示状态栏
  if (status === 'connected' && !connectionError) {
    return null
  }

  const getStatusConfig = () => {
    switch (status) {
      case 'connecting':
        return {
          icon: <Loader2 className="w-4 h-4 animate-spin" />,
          text: '正在连接服务器...',
          bgColor: 'bg-yellow-50',
          textColor: 'text-yellow-800',
          borderColor: 'border-yellow-200'
        }
      
      case 'reconnecting':
        return {
          icon: <Loader2 className="w-4 h-4 animate-spin" />,
          text: `重连中... (${reconnectAttempts}/5)`,
          bgColor: 'bg-yellow-50',
          textColor: 'text-yellow-800',
          borderColor: 'border-yellow-200'
        }
      
      case 'error':
        return {
          icon: <AlertCircle className="w-4 h-4" />,
          text: connectionError || '连接出错',
          bgColor: 'bg-red-50',
          textColor: 'text-red-800',
          borderColor: 'border-red-200'
        }
      
      case 'disconnected':
        return {
          icon: <WifiOff className="w-4 h-4" />,
          text: '服务器连接断开',
          bgColor: 'bg-gray-50',
          textColor: 'text-gray-800',
          borderColor: 'border-gray-200'
        }
      
      default:
        return {
          icon: <Wifi className="w-4 h-4" />,
          text: '已连接',
          bgColor: 'bg-green-50',
          textColor: 'text-green-800',
          borderColor: 'border-green-200'
        }
    }
  }

  const config = getStatusConfig()
  const showRetryButton = status === 'error' || status === 'disconnected'

  return (
    <div className={`${config.bgColor} ${config.borderColor} border-b px-4 py-2`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className={config.textColor}>
            {config.icon}
          </div>
          <span className={`text-sm font-medium ${config.textColor}`}>
            {config.text}
          </span>
        </div>

        {showRetryButton && (
          <button
            onClick={forceReconnect}
            className={`text-xs px-3 py-1 rounded-full border transition-colors ${
              config.textColor === 'text-red-800'
                ? 'border-red-300 text-red-700 hover:bg-red-100'
                : 'border-gray-300 text-gray-700 hover:bg-gray-100'
            }`}
          >
            重试连接
          </button>
        )}
      </div>

      {/* 连接提示 */}
      {status === 'error' && (
        <div className="mt-2 text-xs text-red-600">
          请检查网络连接或稍后重试
        </div>
      )}
    </div>
  )
}

export default ConnectionStatus
