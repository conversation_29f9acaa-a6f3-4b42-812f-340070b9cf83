{"name": "smartinput-mobile", "version": "1.0.0", "description": "SmartInput mobile PWA for voice input", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "serve": "vite preview --port 3000", "lint": "eslint src --ext js,jsx,ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit", "test": "vitest"}, "dependencies": {"clsx": "^2.0.0", "cors": "^2.8.5", "dotenv": "^17.0.1", "express": "^5.1.0", "helmet": "^8.1.0", "idb": "^7.1.1", "lucide-react": "^0.263.1", "multer": "^2.0.1", "qr-scanner": "^1.4.2", "qrcode": "^1.5.4", "rate-limiter-flexible": "^7.1.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.2", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "uuid": "^11.1.0", "workbox-window": "^7.0.0", "zustand": "^4.5.7"}, "devDependencies": {"@types/node": "^24.0.10", "@types/qrcode": "^1.5.5", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.14", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.27", "tailwindcss": "^3.3.3", "typescript": "^5.8.3", "vite": "^4.4.5", "vite-plugin-pwa": "^0.16.4", "vitest": "^0.34.1"}, "engines": {"node": ">=16.0.0"}}